//charge le calendrier et rempli les dates
var mycalendar;
var DateSelected;
var HourSelected;
var configSpinner = {
    decrementButton: "<strong>-</strong>",
    incrementButton: "<strong>+</strong>",
    groupClass: "w-auto customSpinner",
    buttonsClass: "btn-secondary",
    buttonsWidth: "2.5rem",
    textAlign: "center",
    autoDelay: 500,
    autoInterval: undefined,
    /*boostThreshold: 10,
    boostMultiplier: "auto",*/
    locale: null
}

$(document).ready(function () {

    console.log('session.js READY')
    initChangeModalAndCollapse()
    // si le calendrier existe (donc s'il y a au moins une session), on créer la/les date(s) et on le rempli
    //FIX : on appele le calendrier dans le LessReady et dans le window.ready() pour etre sur d'avoir les TRADS + le less
    loadCalendar()

    //si une selection de places a été sauvegardée (commencant par SeatsAutoBasketSelection_), on la récupère
    var basketPreSelectionKey;
    var basketPreSelectionValue;
    for (var i = 0, len = sessionStorage.length; i < len; i++) {
        var key = sessionStorage.key(i);
        var value = sessionStorage[key];
        if (key.startsWith("SeatsAutoBasketSelection_" + structureId + "_" + eventId)) {
            basketPreSelectionKey = key;
            basketPreSelectionValue = JSON.parse(value);
            break;
        }
    }

    for (var i = 0, len = sessionStorage.length; i < len; i++) {
        var key = sessionStorage.key(i);
        if (key.startsWith("SeatsAutoBasketSelection_")) {
            sessionStorage.removeItem(key);
        }
    }
    var sessionsDataValues = []
    $.each(sessionsData, function (i, k) {
        $.each(JSON.parse(k.value), function (iv, kv) {
            sessionsDataValues.push(kv)
        })
    })
    var sessionForcedDataFound = $.grep(sessionsDataValues, function (x) {
        return x.SessionId == ForceSession
    })
    if (basketPreSelectionValue != undefined && basketPreSelectionValue.length > 0 && eventId == parseInt(basketPreSelectionKey.split('_')[2])) {
        //si, pour cet event, une selection de places (commencant par SeatsAutoBasketSelection_) a été sauvegardée (par ex avant une action de login), on lance directement le filtre ZES + grille tarif + ouverture de la modal sélection des consommateurs adhérants
        var sessionIdInProgress = parseInt(basketPreSelectionKey.split('_')[3]);
        sessionStorage.removeItem(basketPreSelectionKey);
        ChoiceAutoOrPlanOrFreePlacement(sessionIdInProgress, false, basketPreSelectionValue)

        SwitchToIndex('#calendarCategsPricesWrapper', '#categsAndPricesWrapper', 0);
        if (identityId != 0) {
            //si l'utilisateur a une identité, on ouvre directement la sélection des consommateurs pour les tarifs adhérant
            openModalLinkConsumersToPrices(structureId, eventId, sessionIdInProgress, identityId, webUserId, buyerProfilId, langCode, partnerToken, basketPreSelectionValue)
        } /*else {
            //si l'utilisateur n'a pas d'identité, on ouvre la demande de login/inscription
            openModalLoginBecomeAdherant(structureId, eventId, sessionIdInProgress, basketPreSelectionValue)
        }*/
    } else if (ForceSession != 0 && sessionForcedDataFound.length > 0 && sessionForcedDataFound[0].Dispo > 0 && !sessionForcedDataFound[0].EventIsLock && !sessionForcedDataFound[0].SessionIsLock) {
        //si la session est forcé via ForceSession, on lance directement le filtre ZES + grille tarif
        ChoiceAutoOrPlanOrFreePlacement(ForceSession)

        SwitchToIndex('#calendarCategsPricesWrapper', '#categsAndPricesWrapper', 0);
    } else if (sessionsData.length == 1 && JSON.parse(sessionsData[0].value).length == 1 && JSON.parse(sessionsData[0].value)[0].Dispo > 0 && !JSON.parse(sessionsData[0].value)[0].EventIsLock && !JSON.parse(sessionsData[0].value)[0].SessionIsLock) {
        //si la session est unique, on lance directement le filtre ZES + grille tarif
        ChoiceAutoOrPlanOrFreePlacement(JSON.parse(sessionsData[0].value)[0].SessionId, true)

        SwitchToIndex('#calendarCategsPricesWrapper', '#categsAndPricesWrapper', 0);
    } else if (sessionsData.length == 1 && JSON.parse(sessionsData[0].value).length == 1 && (JSON.parse(sessionsData[0].value)[0].Dispo == 0 || JSON.parse(sessionsData[0].value)[0].EventIsLock || JSON.parse(sessionsData[0].value)[0].SessionIsLock)) {
        //si la session est unique, et qu'il n'y a pas de dispo ou evenement bloqué ou session bloqué
        var thisDispo = JSON.parse(sessionsData[0].value)[0].Dispo || 0;
        var thisSessionIsLock = JSON.parse(sessionsData[0].value)[0].SessionIsLock;
        var thisEventIsLock = JSON.parse(sessionsData[0].value)[0].EventIsLock;
        var Msg = ""
        if (thisEventIsLock) {
            //event lock
            Msg = JSON.parse(sessionsData[0].value)[0].EventIsLockMessage
        } else if (thisSessionIsLock) {
            //session lock
            Msg = JSON.parse(sessionsData[0].value)[0].SessionIsLockMessage
        } else if (thisDispo == 0) {
            //plus de disponibilités
            Msg = ((GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") : 'Plus de place disponible')
        }
        $('#wdgInsertGrilleTarif').addClass("text-center").html(Msg)
        $("#addPlacesWrapper").hide()
    }



});
// LESSREADY  se déclenche lorsque le LESS/CSS a fini de charger, important pour une modification graphique via JS
// (custom pour la page "Events")
function lessReady() {
    console.log('session.js lessReady READY')
    // si le calendrier existe (donc s'il y a au moins une session), on créer la/les date(s) et on le rempli
    //FIX : on appele le calendrier dans le LessReady et dans le window.ready() pour etre sur d'avoir les TRADS + le less
    loadCalendar()
    // Appliquer la correction pour les éléments .noevent
    setTimeout(fixNoeventElements, 100);
}

//initialisation du calendrier
function loadCalendar() {
    if (sessionsData.length > 0) {
        //on reset le calendrier
        $('#calendarjsWrapper').html('<div id="calendarjs"></div>')
        var now = new Date();
        var year = now.getUTCFullYear();
        var month = now.getUTCMonth() + 1;
        var date = now.getUTCDate();

        var actualDate;
        var actualDateFormatToClick;
        //date by forceDate
        var ForceDateDay = ForceDate.substring(6) || "01";
        var ForceDateMonth = ForceDate.substring(4, 6);
        var ForceDateYear = ForceDate.substring(0, 4);

        if (parseInt(ForceDate) != 0 && !isNaN(new Date(ForceDateYear, ForceDateMonth - 1, ForceDateDay))) {
            //si une date a été forcé par ForceDate
            actualDate = new Date(ForceDateYear, ForceDateMonth - 1, ForceDateDay);
        } else if (!isNaN(new Date(sessionsData[0].date))) {
            //sinon on récupère la première date des datas
            actualDate = new Date(sessionsData[0].date);
        } else {
            //sinon on prend la date d'aujourd'hui
            actualDate = new Date(year, month - 1, date + 1);
        }
        actualDateFormatToClick = actualDate.getDate() + '/' + (actualDate.getMonth() + 1) + '/' + actualDate.getFullYear();

        // fix si une seule séance avec dispo = 0 : TODO à faire + joli
        if (sessionsData.length == 1 && sessionsData[0].LstSessions.length == 1 && sessionsData[0].LstSessions[0].Dispo == 0) {
            thishtml = ((GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") : 'Plus de place disponible');
            $('#oneManifs').html(thishtml);
        }


        var calendarOptions = {
            date: actualDate,
            width: "100%",
            view: 'date',
            label: false,
            format: 'dd/mm/yyyy',
            startWeek: 1,
            prev: '<i class="fas fa-chevron-left"></i>',
            next: '<i class="fas fa-chevron-right"></i>',
            weekArray: getAllDaysLocales(),
            monthArray: getAllMonthLocales(),
            data: sessionsData,
            viewChange: function (view, y, m) {
                $('.days [data-calendar-day]').removeClass('selected');
                unloadHours()
                DateSelected = ""
            },
            onSelected: function (view, date, data) {
                if (view == "date" && DateSelected != date.toString() && !$(this).hasClass('disabled')) {
                    loadingButtonBootstrapOn(this)
                    DateSelected = date.toString()
                    unloadHours()
                    if (data != '' && data != null) {
                        loadHours(data)
                    } else {
                        loadingButtonBootstrapOff($('#calendarjsWrapper .days li'))
                        $('#alleventhours').append("<span class='noevent'>" + ((GetTranslationTerm(TranslationsList, "Widget_Session_LblNoSession") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblNoSession") : 'Pas de séance ce jour') + "</span>")
                        // Appliquer la correction pour le nouvel élément .noevent
                        setTimeout(fixNoeventElements, 10);
                        sendIframeSize()
                    }
                }
            }
        }
        $('#calendarjs').calendar(calendarOptions);
        //force la date pointé sur le calendrier
        if (SettingsMerge.sessions.firstDateAutoClicked) {
            $('#calendarjs').calendar('setDateClick', actualDateFormatToClick)
        }

        sendIframeSize();

    }
    //console.log('calendar loaded')
}

//retourne un tableau de tous les mois de l'année traduit en fonction du langCode
function getAllMonthLocales() {
    var allmonths = []
    for (i = 0; i <= 11; i++) {
        var objDate = new Date();
        objDate.setDate(1);
        objDate.setMonth(i);
        allmonths.push(objDate.toLocaleString(langCode, { month: "short" }));

    }
    return allmonths;
}
//retourne un tableau de tous les jours de la semaine traduit en fonction du langCode (commence par un dimanche par defaut)
function getAllDaysLocales() {
    var alldays = [];
    d = new Date();
    var day = d.getDay(),
        diff = d.getDate() - day;
    d.setDate(diff);

    for (let i = 0; i < 7; i++) {
        let x = new Date(d);
        x.setDate(x.getDate() + i);
        let dayshort = x.toLocaleString(langCode, { weekday: "short" })
        alldays.push(dayshort);
    }

    return alldays;
}
// vide les seances
function unloadHours() {

    $('#AutoAndSeatPlan li:first-child a').tab('show')
    $('#wdgInsertSeatPlan').html('')
    loadingBootstrapOn('#wdgInsertSeatPlan')
    $('#alleventhourstitle').hide()
    $('#alleventhours').html('');
    HourSelected = "";
    $('#allCategs').html("");
    $("#wdgInsertZoneFloorSection").html("");
    $(".SeePlanSalleGrilleTarif").html("");
    loadingBootstrapOn('.SeePlanSalleGrilleTarif')
    $('#wdgInsertGrilleTarif').html('')
    loadingBootstrapOn('#wdgInsertGrilleTarif')
    $('#mapGrilleTarifInner').html('')
    loadingBootstrapOn('#mapGrilleTarifInner')
    sendIframeSize();
    $('#calendarjsWrapper .days li').removeClass("disabled")
}

// charge les seances de la date sélectionnée
function loadHours(data) {
    var data = JSON.parse(data)
    //tri par heure et minute
    data.sort(predicate({
        name: 'Hour',
        reverse: false
    }, 'Minute'));
    var datalength = data.length;
    var showLockedMessage = false;
    var showHours = true;
    $.each(data, function (i, k) {
        var hourMsgArr = []
        //afficher nom du lieu
        var showPlaceName = true
        if (SettingsMerge.sessions.showPlaceName[k.LieuId.toString()] != undefined) {
            showPlaceName = SettingsMerge.sessions.showPlaceName[k.LieuId.toString()]
        } else if (SettingsMerge.sessions.showPlaceName["default"] != undefined) {
            showPlaceName = SettingsMerge.sessions.showPlaceName["default"]
        }
        //on récupère quelques options de chaque session
        var sessionDataFound = $.grep(AllSessions, function (x) {
            return x.sessionId == k.SessionId
        })

        var thisDispo = k.Dispo || 0;
        var thisSessionIsLock = k.SessionIsLock;
        var thisEventIsLock = k.EventIsLock;
        if (thisSessionIsLock || thisEventIsLock) {
            showLockedMessage = true;
        }
        var thisClass = "onehour";
        var thisHour = ("0" + k.Hour).slice(-2) + ":" + ("0" + k.Minute).slice(-2);
 
        //si la seance est unique pour cette date + dispo supérieur a 0
        if (sessionDataFound[0] != null && datalength == 1 && thisDispo > 0) {
            if (!sessionDataFound[0].isShowSessionHour) {
                //si isShowSessionHour est sur false alors on masque la date
                showHours = false;
            }
        }

        if (showHours) {
            hourMsgArr.push(thisHour)
        }
        

        if (showPlaceName) {
            hourMsgArr.push(k.LieuName)
            hourMsgArr = [hourMsgArr.join(', ')]
        }
        
        if (thisEventIsLock) {
            //event lock
            hourMsgArr.push(k.EventIsLockMessage)
            hourMsgArr = [hourMsgArr.join(" - ")]
            thisClass = "onehour notavailable";
        } else if (thisSessionIsLock) {
            //session lock
            hourMsgArr.push(k.SessionIsLockMessage)
            hourMsgArr = [hourMsgArr.join(" - ")]
            thisClass = "onehour notavailable";
        } else if (thisDispo == 0) {
            //plus de disponibilités
            hourMsgArr.push(((GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblSessionNoDispo") : 'Plus de place disponible'))
            hourMsgArr = [hourMsgArr.join(" - ")]
            thisClass = "onehour notavailable nodisponible";
        }
        
        var html = ''
        html = "<span class='" + thisClass + "' data-placeid='" + k.LieuId + "' data-sessionid='" + k.SessionId + "' data-dispo='" + thisDispo + "' data-hour='" + thisHour + "'>"
        html += "<span class='beforehover'>" + hourMsgArr.toString() + "</span>"
        html += "<span class='invisible'>" + hourMsgArr.toString() + "</span>"
        html += "<span class='afterhover'>" + hourMsgArr.toString() + "</span>"
        html += "</span>"
        if (showHours || showLockedMessage) {
            $('#alleventhours').append(html)
            $('#alleventhours .onehour').fadeOut(0)
        }
    })
    if (showHours || !showHours && showLockedMessage) {
        loadingButtonBootstrapOff($('#calendarjsWrapper .days li'))
        $('#alleventhourstitle').fadeIn()
    } else {
        ChoiceAutoOrPlanOrFreePlacement(data[0].SessionId);
    }


    //bind le click sur chaque horaires
    $.each($('#alleventhours .onehour'), function (i, k) {
        $(k).fadeIn(150 * (i + 1))
        $(k).off("click").on("click", function () {
            if (!$(this).hasClass("disabled")) {
                clickOneHour($(k))
            }
        })
        //si la seance est unique pour cette date ET que l'option oneHourAutoClicked est sur true alors autoclick
        if (datalength == 1 && SettingsMerge.sessions.oneHourAutoClicked && parseInt($(k).attr('data-dispo')) > 0) {
            clickOneHour($(k))
        }
    })
    sendIframeSize();
}
//actions du bind des horaires/session du calendrier
function clickOneHour(element) {
    if (!element.hasClass('notavailable')) {
        if (HourSelected != element.data('sessionid')) {
            sessionId = element.data('sessionid');
            $('#alleventhours .onehour').removeClass("selected");
            element.addClass('selected');
            $('#alleventhours .onehour').not(element).addClass("disabled")
            $('#calendarjsWrapper .days li').addClass("disabled")
            loadingButtonBootstrapOn(element.find('.afterhover'))
            ChoiceAutoOrPlanOrFreePlacement(sessionId);
        }
    }
}

//verifie si on est en choix auto et/ou en choix sur plan ou placementlibre
function ChoiceAutoOrPlanOrFreePlacement(sessionid, isUniqueSession, basketPreSelection) {
    //trigger le parent du widget de load le commentaire seance
    var msg = {
        "action": "getCommentairesSeance",
        "sessionId": sessionid
    }
    window.parent.postMessage(msg, '*')


    var isUniqueSession = (isUniqueSession == true) ? isUniqueSession : false;
    var sessionid = parseInt(sessionid) || parseInt($('.onehour.selected').attr('data-sessionid'));
    //recuperation de la session
    var thisSession = $.grep(AllSessions, function (x) {
        return x.sessionId == sessionid;
    })

    //écriture de la date + horaires + lieu pour la séance sélectionnée
    var thisSessionIdDatas = []
    $.each(sessionsData, function (i, k) {
        $.each(JSON.parse(k.value), function (iv, kv) {
            if (sessionid == kv.SessionId) {
                thisSessionIdDatas.push(kv);
                return false;
            }
        })
    })
    if (thisSessionIdDatas.length > 0) {
        var thisSessionYear = parseInt(thisSessionIdDatas[0].Date.toString().split('-')[0]);
        var thisSessionMonth = parseInt(thisSessionIdDatas[0].Date.toString().split('-')[1]) - 1;
        var thisSessionDay = parseInt(thisSessionIdDatas[0].Date.toString().split('-')[2]);
        var thisSessionHour = thisSessionIdDatas[0].Hour;
        var thisSessionMinute = thisSessionIdDatas[0].Minute;
        var thisSessionIdDate = new Date(thisSessionYear, thisSessionMonth, thisSessionDay, thisSessionHour, thisSessionMinute, 0);
        var thisSessionIdLieuName = thisSessionIdDatas[0].LieuName;

        let thisSessionOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit' };
        let thisSessionOptionsNohour = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };

        //afficher nom du lieu
        var showPlaceName = true
        if (SettingsMerge.sessions.showPlaceName[thisSessionIdDatas[0].LieuId.toString()] != undefined) {
            showPlaceName = SettingsMerge.sessions.showPlaceName[thisSessionIdDatas[0].LieuId.toString()]
        } else if (SettingsMerge.sessions.showPlaceName["default"] != undefined) {
            showPlaceName = SettingsMerge.sessions.showPlaceName["default"]
        }

        if (AllSessions.length == 1 && !thisSession[0].isShowSessionDate) {
            //si la session est unique et que le showsessiondate est sur false, on cache la date + lieu
            $("#selectedSeance").hide()
        } else if (!thisSession[0].isShowSessionHour) {
            //si la session est unique et que le showsessionhour est sur false, on cache l'heure
            var html = thisSessionIdDate.toLocaleString(langCode, thisSessionOptionsNohour)
            if (showPlaceName) {
                html += " - " + thisSessionIdLieuName
            }
            $("#selectedSeanceDate").html(html)
        } else {
            //sinon on affiche tout
            var html = thisSessionIdDate.toLocaleString(langCode, thisSessionOptions)
            if (showPlaceName) {
                html += " - " + thisSessionIdLieuName
            }
            $("#selectedSeanceDate").html(html)
        }

    }
    // bind click pour modifier la date précédement sélectionée (sauf si la date est unique)
    if (!isUniqueSession) {
        $("#selectedSeance").addClass('linked')
        $("#selectedSeance").off('click').on('click', function (e) {
            e.preventDefault();
            shouldUnflagSeats(function () {
                $('#alleventhourstitle').hide()
                $('#alleventhours').html('')
                DateSelected = "";
                SwitchToIndex('#calendarCategsPricesWrapper', '#calendarWrapper', 300, function () {
                    unloadHours()
                })
                $(".SeePlanSalleGrilleTarif").collapse('hide')
            })
            $('#calendarjs .days li').removeClass('selected')
        })
    }

    if (thisSession.length > 0) {
        thisSession = thisSession[0]
        $("#TitleFreePlacement").addClass("d-none")
        $("#TitleChoiceAuto").addClass("d-none")
        $("#TitleChoicePlan").addClass("d-none")
        $("#AutoAndSeatPlan").addClass("d-none")
        $(".SeePlanSalleGrilleTarif_Wrapper").addClass("d-none")
        if (thisSession.isFullPlacementLibre) {
            //si placement libre uniquement
            freePlacement = true
            $("#TitleFreePlacement").removeClass("d-none")
            //refreshGrilleTarif(sessionid, isUniqueSession, basketPreSelection, 'auto');
            refreshZoneFloorSection(sessionid, isUniqueSession, basketPreSelection);
        } else {
            if (thisSession.containsAutomatique && thisSession.containsSurPlan) {
                //si choix auto ET choix sur plan
                $("#AutoAndSeatPlan").removeClass("d-none")
                $(".SeePlanSalleGrilleTarif_Wrapper").removeClass("d-none")

                if (SettingsMerge.sessions.reverseTabsAutoAndSeatPlan == true) {
                    SeatsPlanAjax(thisSession, isUniqueSession)
                    SwitchToIndex('#calendarCategsPricesWrapper', '#categsAndPricesWrapper', 300)
                    $("#plan-tab").tab('show')
                } else {
                    refreshZoneFloorSection(sessionid, isUniqueSession, basketPreSelection);
                }

            } else if (thisSession.containsAutomatique) {
                //si choix auto
                $("#TitleChoiceAuto").removeClass("d-none")
                $(".SeePlanSalleGrilleTarif_Wrapper").removeClass("d-none")
                refreshZoneFloorSection(sessionid, isUniqueSession, basketPreSelection);
            } else if (thisSession.containsSurPlan) {
                //si choix sur plan
                $("#TitleChoicePlan").removeClass("d-none")
                SeatsPlanAjax(thisSession, isUniqueSession)
                SwitchToIndex('#calendarCategsPricesWrapper', '#categsAndPricesWrapper', 300)
                $("#plan-tab").tab('show')
            }

        }
        $('#AutoAndSeatPlan a').off('click').on('click', function (e) {
            e.preventDefault()
            var thistab = $(this)
            var oldtab = $('#AutoAndSeatPlan a.active')
            if ($(thistab).attr('id') == "plan-tab" && oldtab.attr('id') != "plan-tab" && !($('#panoInner').length > 0 || $('#bigMapWrapper').length > 0)) {
                SeatsPlanAjax(thisSession, isUniqueSession)
                $(thistab).tab('show')
            } else if ($(thistab).attr('id') != "plan-tab") {
                //supprime la partie plan lorsqu'on la quitte (fix a cause d'un bug pano)
                if ($('#panoInner').length > 0) {
                    $('#wdgInsertSeatPlan').html("")
                    loadingBootstrapOn('#wdgInsertSeatPlan')
                }
                //unflags
                shouldUnflagSeats(function () {
                    $(thistab).tab('show')
                    if (SettingsMerge.sessions.reverseTabsAutoAndSeatPlan == true) {
                        $('#wdgInsertGrilleTarif').html("")
                        refreshZoneFloorSection(sessionid, isUniqueSession, basketPreSelection);
                    }
                    if ($("#panoInner").html() == "") {
                        SwitchToIndex('#panoAndBigMapWrapper', '#bigMapWrapper', 300)
                        $('#resumePreShoppingCartPLAN .alertMaxSeatReached').find(".alertwrapperMaxSeatReached").remove()
                    } else {
                        SwitchToIndex('#panoAndBigMapWrapper', '#panoWrapper', 300)
                    }
                })
            } else {
                $(thistab).tab('show')
            }
        })
        $('#AutoAndSeatPlan a').on('shown.bs.tab', function (e) {
            $(e.target).data('otherhref') // newly activated tab
            $($(e.target).data('otherhref')).addClass('show active')
            $($(e.relatedTarget).data('otherhref')).removeClass('show active')
            sendIframeSize();
        })
    }
}

//charge la view pour initialiser la partie plan
function SeatsPlanAjax(session, isUniqueSession) {
    var sessionId = session.sessionId
    $.ajax({
        type: "GET",
        url: sWOffersUrl + "SeatsPlanAjax/" + structureId + "/" + eventId + "/" + sessionId + "/" + identityId + "/" + webUserId + "/" + buyerProfilId + "/" + langCode,
        data: {
            token: partnerToken
        },
        success: function (data) {
            $('#wdgInsertSeatPlan').html(data)
            initPanoOrPlan(session, isUniqueSession)
            sendIframeSize();
        },
        error: function (a, b, c) {
            console.log("SeatsPlanAjax -> Error")
        }
    });
}

//load le filtre zone etage section
//(session id, la session est-elle unique, une pre-selection du panier, forceZoneId, forceFloorId, forceSectionId)
function refreshZoneFloorSection(sessionid, isUniqueSession, basketPreSelection, forceZoneId, forceFloorId, forceSectionId, openCategId) {
    var isUniqueSession = (isUniqueSession == true) ? isUniqueSession : false;
    var sessionid = parseInt(sessionid) || parseInt($('.onehour.selected').attr('data-sessionid'));
    console.log(forceZoneId + " , " + forceFloorId + " , " + forceSectionId)
    $("#wdgInsertZoneFloorSection").html("");
    loadingBootstrapOn('#wdgInsertZoneFloorSection')
    $.ajax({
        type: "GET",
        url: sWOffersUrl + "RefreshZoneFloorSectionAjax/" + structureId + "/" + langCode + "/" + eventId + "/" + sessionid + "/" + identityId + "/" + webUserId + "/" + buyerProfilId,
        data: {
            token: partnerToken
        },
        success: function (data) {
            $("#wdgInsertZoneFloorSection").html(data);
            //si une préselection existe, on force les ZES sur ces valeurs
            var basketPreSelectionZoneId = 0;
            var basketPreSelectionFloorId = 0;
            var basketPreSelectionSectionId = 0;
            if (basketPreSelection != undefined && basketPreSelection.length > 0) {
                basketPreSelectionZoneId = basketPreSelection[0].ZoneId;
                basketPreSelectionFloorId = basketPreSelection[0].FloorId;
                basketPreSelectionSectionId = basketPreSelection[0].SectionId;
            }

            if ($('#zoneSelect option').length > 0) {
                if (basketPreSelectionZoneId > 0) {
                    $('#zoneSelect').val(basketPreSelectionZoneId)
                } else if (forceZoneId != undefined) {
                    $('#zoneSelect').val(forceZoneId)
                }
                $('#zoneSelect').on('change', function () {
                    ManageFiltersZES("#floorSelect", $(this).find('option:selected').attr('data-childrens'), sessionid, isUniqueSession, basketPreSelection)
                    //refreshGrilleTarif(sessionid, isUniqueSession, undefined, 'auto');
                });

            }
            if ($('#floorSelect option').length > 0) {
                if (basketPreSelectionFloorId > 0) {
                    $('#floorSelect').val(basketPreSelectionFloorId)
                } else if (forceFloorId != undefined) {
                    $('#floorSelect').val(forceFloorId)
                }
                $('#floorSelect').on('change', function () {
                    ManageFiltersZES("#sectionSelect", $(this).find('option:selected').attr('data-childrens'), sessionid, isUniqueSession, basketPreSelection)
                    //refreshGrilleTarif(sessionid, isUniqueSession, undefined, 'auto');
                });
            }
            if ($('#sectionSelect option').length > 0) {
                if (basketPreSelectionSectionId > 0) {
                    $('#sectionSelect').val(basketPreSelectionSectionId)
                } else if (forceSectionId != undefined) {
                    $('#sectionSelect').val(forceSectionId)
                }
                $('#sectionSelect').on('change', function () {
                    refreshGrilleTarif(sessionid, isUniqueSession, undefined, 'auto');
                });
            }
            if (openCategId != undefined) {
                refreshGrilleTarif(sessionid, isUniqueSession, basketPreSelection, 'auto', [], openCategId);
            } else {
                refreshGrilleTarif(sessionid, isUniqueSession, basketPreSelection, 'auto');
            }
            sendIframeSize();
        },
        error: function (a, b, c) {
            SwitchToIndex('#calendarCategsPricesWrapper', '#categsAndPricesWrapper', 300)
            $('#wdgInsertGrilleTarif').html('')
            var html = "";
            html += "<div class='text-center'>";
            html += "<div class='alert alert-danger d-inline-block'>";
            html += ((GetTranslationTerm(TranslationsList, "Widget_Session_MsgErrorRefreshZoneFloorSectionAjax") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_MsgErrorRefreshZoneFloorSectionAjax") : "Désolé, il semblerait qu'il y ait une erreur au chargement de la grille de tarifs (trad)")
            //html += "<br/><small>" + a.responseText + "</small>"
            html += "</div>";
            html += "</div>";
            $("#wdgInsertZoneFloorSection").html(html);
            console.log("RefreshZoneFloorSectionAjax -> Error")
            sendIframeSize();
        }
    });
}

//change le choix des filtres (Zones, étages, sections) + trigger en cascade
//le select zone change le select étage
//le select étage change le select section
//le select section trigger refreshGrilleTarif
function ManageFiltersZES(target, childrensArr, sessionid, isUniqueSession, basketPreSelection) {
    $(target).find('option').show()
    if (childrensArr != undefined) {
        var childrensArr = childrensArr.split(',')
        $(target).find('option')
            .filter(function (i, e) {
                return ($.inArray(e.value, childrensArr) == -1 && e.value != 0);
            }).hide()
    }
    $(target).find('option').eq(0).prop('selected', true);


    var selectArr = ["#zoneSelect", "#floorSelect", "#sectionSelect"]
    var index = $.inArray(target, selectArr);
    for (i = index; i < selectArr.length; i++) {
        if ($(selectArr[i]).length > 0) {
            $(selectArr[i]).trigger('change')
            return false;
        }
        if ($(selectArr[i]).length == 0 && i == selectArr.length - 1) {
            refreshGrilleTarif(sessionid, isUniqueSession, basketPreSelection, 'auto');
        }
    }

}

//chargement de la grille de tarifs
function refreshGrilleTarif(sessionid, isUniqueSession, basketPreSelection, tab, categIds, openCategId) {



    switch (tab) {
        case 'auto':
            htmlTarget = "#wdgInsertGrilleTarif"
            break;
        case 'plan':
            htmlTarget = "#mapGrilleTarifInner"
            break
    }
    $(htmlTarget).html('');
    loadingBootstrapOn(htmlTarget)
    var isUniqueSession = (isUniqueSession == true) ? isUniqueSession : false;
    var sessionid = sessionid || parseInt($('.onehour.selected').attr('data-sessionid'));
    var zoneid = parseInt($('#zoneSelect').val()) || 0;
    var floorid = parseInt($('#floorSelect').val()) || 0;
    var sectionid = parseInt($('#sectionSelect').val()) || 0;
    var categIds = (categIds != undefined) ? categIds : []
    $.ajax({
        type: "POST",
        url: sWOffersUrl + "RefreshGrilleTarifAjax/" + structureId + "/" + langCode + "/" + eventId + "/" + sessionid + "/" + identityId + "/" + webUserId + "/" + buyerProfilId + "/" + zoneid + "/" + floorid + "/" + sectionid,
        data: {
            tab: tab,
            categsId: categIds,
            token: partnerToken
        },
        success: function (data) {
            //écrit la grille de tarif
            initGrilleTarif(data, sessionid, isUniqueSession, basketPreSelection, tab, openCategId)
            // charge le plan "voir le plan de salle"
            if (SettingsMerge.sessions.showSeePlan) {
                loadSeePlan(sessionid, '.SeePlanSalleGrilleTarif');
            }
        },
        error: function (a, b, c) {
            console.log("refreshGrilleTarif -> Error")
            loadingBootstrapOff(htmlTarget)
            jQuery(htmlTarget).html(a.responseText);
        }
    });

}
//remplissage de la grille des tarifs + bind du bouton ajouter au panier sous les tarifs
function initGrilleTarif(data, sessionId, isUniqueSession, basketPreSelection, tab, openCategId) {

    loadingButtonBootstrapOff($('#calendarjsWrapper .days li'))

    var htmlTarget = "";
    var addCartWrapper = ""
    switch (tab) {
        case 'auto':
            htmlTarget = "#wdgInsertGrilleTarif"
            addCartWrapper = "#resumeAddShoppingCartAUTO"
            break;
        case 'plan':
            htmlTarget = "#mapGrilleTarifInner"
            addCartWrapper = "#resumeAddShoppingCartPLAN"
            break
    }
    $(htmlTarget).html(data);
    $(htmlTarget + ' [data-toggle="tooltip"]').tooltip({
        template: '<div class="tooltip tooltip-lg" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>'
    })

    //bind l'ajout des places auto dans le panier
    $(addCartWrapper + " .addPlaces").off('click').on('click', function () {
        if (!$(this).hasClass('disabled')) {
            //on créer un nouvel objet de sélection des ZES + places
            var BasketSelection = CreateBasketSelection(htmlTarget, tab);
            loadingButtonBootstrapOn($(this))
            if (selectionHasPricesAvantage(BasketSelection)) {
                //1- si il y a des tarifs avantage dans la selection de l'utilisateur
                openModalPricesAdvantage(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection)
            } else if (selectionHasPricesFeedBook(BasketSelection)) {
                //2- si il y a des tarifs carnet à tickets dans la selection de l'utilisateur
                openModalPricesFeedBook(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection)
            } else if (selectionHasPricesAdhesion(BasketSelection)) {
                //3- si il y a des tarifs adhesion dans la selection de l'utilisateur, on ouvre soit la modal pour logger soit pour linker les consommateurs
                openModalAdhesionSwitch(structureId, eventId, sessionId, identityId,  webUserId, buyerProfilId, langCode, partnerToken, BasketSelection)
            } else {
                //4- sinon on ajoute directement au panier
                addToBasket(structureId, eventId, sessionId, identityId, Basket.basketId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection)
            }
        }
    });
    //formatage des devises
    formatDevise()
    CacherPrixCarteAdhesion();

    //si la grilletarif est chargé via le plan (arrSeatsFlagged contient des places), on configure des limites de places prenable par categ
    if (arrSeatsFlagged != undefined && arrSeatsFlagged.length > 0 && htmlTarget == "#mapGrilleTarifInner") {
        var arrSeatsFlaggedTmp = {}
        $.each(arrSeatsFlagged, function (i, k) {
            arrSeatsFlaggedTmp[k.categid] = (arrSeatsFlaggedTmp[k.categid] || 0) + 1

        })
        $.each(arrSeatsFlaggedTmp, function (i, k) {
            $(htmlTarget + ' .categWrapper[data-categid="' + i + '"] .seatsByCategoryWrapper').attr("data-maxplaces", k)
            $(htmlTarget + ' .categWrapper[data-categid="' + i + '"] .seatsByCategoryWrapper').attr("data-restplaces", k)
            //si la grilletarif est chargé via le plan + si option preSelectWhenUniquePriceFromPlan est activé + un seul tarif visible sur la grille + si le min du tarif est inférieur ou égal au nombre de sieges sélectionnés
            if (SettingsMerge.sessions.preSelectWhenUniquePriceFromPlan == true && $(htmlTarget + ' .categWrapper[data-categid="' + i + '"] .customNumberInput[type="number"]').length == 1 && $(htmlTarget + ' .categWrapper[data-categid="' + i + '"] .customNumberInput[type="number"]').attr('data-realmin') <= k) {
                $(htmlTarget + ' .categWrapper[data-categid="' + i + '"] .customNumberInput[type="number"]').val(k)
            }
        })
        //calculMaxSeatsToTakeByCateg(htmlTarget)
        calculMaxSeatsToTake(htmlTarget, addCartWrapper, true)
    } else {
        calculMaxSeatsToTake(htmlTarget, addCartWrapper)
    }

    // intialisation des input nombre de place pour chaque tarifs
    $(htmlTarget + " .customNumberInput[type='number']").inputSpinner(configSpinner)
    $(htmlTarget + ' .tarifLigne[data-ischildren="True"] .customNumberInput').prop('disabled', true).prop('readonly', true)

    //lorsque l'utilisateur change la valeur du nombre de place
    $(htmlTarget + ' .customNumberInput').off('change input').on('change input', function (e) {
        //gestion du saut entre le 0 et le min (dans le cas ou le min est supérieur a 0)
        var thisInput = $(this).closest('.categorieSelect').find('.customNumberInput[type="number"]')
        if (e.type == "input") {
            thisInput.val($(thisInput).next().find('.customNumberInput').val())
        }

        if (parseInt($(thisInput).attr('data-realmin')) > 0) {
            if (parseInt($(thisInput).val()) > parseInt($(thisInput).attr('data-oldval')) && parseInt($(thisInput).attr('data-oldval')) == 0) {
                //N'EST PAS exécuté si la grilletarif est chargé via le plan + si option preSelectWhenUniquePriceFromPlan est activé + un seul tarif visible sur la grille + si le min du tarif est inférieur ou égal au nombre de sieges sélectionnés
                if ( !(arrSeatsFlagged != undefined && arrSeatsFlagged.length > 0 && htmlTarget == "#mapGrilleTarifInner" && SettingsMerge.sessions.preSelectWhenUniquePriceFromPlan == true && $(thisInput).closest('.categWrapper').find('.customNumberInput[type="number"]').length == 1 && $(thisInput).closest('.categWrapper').find('.customNumberInput[type="number"]').attr('data-realmin') <= $(thisInput).val()) ) {
                    $(thisInput).val($(thisInput).attr('data-realmin'))
                } 
            }
            if (parseInt($(thisInput).val()) < parseInt($(thisInput).attr('data-oldval')) && parseInt($(thisInput).val()) < parseInt($(thisInput).attr('data-realmin'))) {
                $(thisInput).val(0)
            }
        }

        //si la valeur souhaitée dépasse le max de l'input ou ce qui reste de place a prendre
        if ((parseInt($(thisInput).val()) - parseInt($(thisInput).attr('data-oldval'))) > parseInt($(thisInput).closest('.offerGroup').attr("data-motherrule-max")) ) {
            $(thisInput).val(parseInt($(thisInput).attr('data-oldval')) + parseInt($(thisInput).closest('.offerGroup').attr("data-motherrule-max"))  )
        }
        if (parseInt($(thisInput).val()) > parseInt($(thisInput).attr('data-realmax'))) {
                $(thisInput).val($(thisInput).attr('data-realmax'))
        }
        
        //a chaque changement d'un input, on reparcours tous les inputs pour tout mettre à jour
        $.each($(htmlTarget + ' .customNumberInput[type="number"]'), function (ii, ki) {
            var thisInput = $(ki)
            //lorsque ce tarif est un tarif maitre
            if ($(thisInput).attr("data-ismaster") == "True") {
                var thisMasterVal = parseInt($(thisInput).val())
                var thisMasterMax = parseInt($(thisInput).attr("max"))
                var thisMasterCategId = $(thisInput).attr("data-categid")
                var allchidrens = JSON.parse($(thisInput).attr("data-childrenids"));

                //console.log("thisMasterVal : " + thisMasterVal)

                if (allchidrens.length > 0) {
                    $.each(allchidrens, function (i, k) {
                        //si on prend au moins une place du maitre
                        if (thisMasterVal > 0) {
                            $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').closest('.tarifLigne').addClass('activated')
                            var thisChidrenRodMin = parseInt($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-rodmin')) || 0;
                            var thisChidrenRodMax = parseInt($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-rodmax')) || 0;

                            var thisChidrenMax = parseInt($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('max')) || 0;
                            var thisChidrenMin = parseInt($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('min')) || 0;
                            if ($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-realmax') == undefined) {
                                $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-realmax', thisChidrenMax)
                            }
                            if ($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-realmin') == undefined) {
                                $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-realmin', thisChidrenMin)
                            }
                            var thisChidrenRealMax = parseInt($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-realmax'))
                            var forceChildrenValue = thisMasterVal

                            //si le value du tarif maitre est supérieur au max du tarif élève, alors on reste sur le max de l'élève
                            if (thisMasterVal > thisChidrenMax) {
                                forceChildrenValue = thisChidrenMax
                            }

                            //lorsque le rodmin du tarif élève est égal au rodmax du tarif élève -> alors 1 place tarif maitre = 1 place tarif èlévé forcé
                            //sinon on débloque juste l'élève
                            if (thisChidrenRodMin >= thisChidrenRodMax) {
                                $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').addClass('tarifForced')
                                $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').val(forceChildrenValue)
                                $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').prop('readonly', false)
                                $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').next().find('.customNumberInput').prop('readonly', false)
                            } else {
                                var forecedChildrenMax = thisMasterVal * thisChidrenRodMax

                                if (forecedChildrenMax > thisChidrenRealMax) {
                                    forecedChildrenMax = thisChidrenRealMax
                                }
                                $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-ratiocalculatedmax', forecedChildrenMax)

                                if ($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-maxseatfromplantotake') == 0) {
                                    forecedChildrenMax = 0
                                }


                                //si le rodmin est supérieur à 0, alors le min de l'enfant devient son rodmin multiplé par la valeur du tarif maitre
                                if (thisChidrenRodMin > 0) {
                                    $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('min', thisChidrenRodMin * thisMasterVal)
                                }

                                //si la valeur de l'enfant est supérieur a son maximum autorisé, on le force sur ce max
                                if ($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').val() > forecedChildrenMax) {
                                    $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').val(forecedChildrenMax)
                                }

                                $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('max', forecedChildrenMax)


                                if ($(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('max') == 0) {
                                    //si le max de l'enfant est 0, alors on le desactive
                                    $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').closest('.tarifLigne').removeClass('activated')
                                    $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').prop('disabled', true).prop('readonly', true)
                                    $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').next().find('.customNumberInput').prop('disabled', true).prop('readonly', true)
                                } else {
                                    //sinon on l'active
                                    $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').prop('disabled', false).prop('readonly', false)
                                    $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').next().find('.customNumberInput').prop('disabled', false).prop('readonly', false)
                                }

                            }
                        } else {
                            $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').closest('.tarifLigne').removeClass('activated')
                            $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('min', 0)
                            $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('max', $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').attr('data-realmax'))
                            $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').val(0)
                            $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').prop('disabled', true).prop('readonly', true)
                            $(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').next().find('.customNumberInput').prop('disabled', true).prop('readonly', true)
                        }
                        //$(htmlTarget + ' .customNumberInput[data-priceid="' + k + '"][data-categid="' + thisMasterCategId + '"]').trigger('change')

                    })
                }
            }
            thisInput.attr("data-oldval", thisInput.val())

            //Si le tariflimit est different de 0, on s'assure qu'on ne peut pas prendre + de ce tarif que le tariflimit sur cette séance
            var thisTarifLimit = parseInt($(thisInput).attr('data-tariflimit')) || 0;
            if (thisTarifLimit != 0) {
                var thisOfferId = parseInt($(thisInput).attr("data-offerid"))
                var thisPriceId = parseInt($(thisInput).attr("data-offerid"))
                var typetarif = "";
                if ($(thisInput).attr("data-ismaster") == "True") {
                    typetarif = '[data-ismaster="True"]';
                }
                if ($(thisInput).attr("data-ischildren") == "True") {
                    typetarif = '[data-ischildren="True"]';
                }
                //on compte combien de tarif du même type (maitre ou élève) ont été pris
                var countTarifSameOffer = 0
                $.each($(htmlTarget + ' .customNumberInput' + typetarif + '[data-offerid="' + thisOfferId + '"]'), function (i, k) {
                    if (parseInt($(k).val()) > 0) {
                        countTarifSameOffer++
                    }
                })
                //console.log("countTarifSameOffer : " + countTarifSameOffer + " / thisTarifLimit : " + thisTarifLimit)
                //si l'utilisateur a pris autant ou + de tarif du même type (maitre ou élève) que le tariflimit, on désactive tous les autres du même type pour cette offre
                if (countTarifSameOffer >= thisTarifLimit) {
                    var allTarifToDisabled = $(htmlTarget + ' .customNumberInput' + typetarif + '[data-offerid="' + thisOfferId + '"]').filter(function () {
                        return parseInt($(this).val()) == 0;
                    });
                    $.each(allTarifToDisabled, function (i, k) {
                        $(k).prop('disabled', true).prop('readonly', true)
                        $(k).next().find('.customNumberInput').next().find('.customNumberInput').prop('disabled', true).prop('readonly', true)
                    })
                } else {
                    $(htmlTarget + ' .tarifLigne' + typetarif + '[data-offerid="' + thisOfferId + '"].activated').find('.customNumberInput').not('.tarifForced').prop('disabled', false).prop('readonly', false)
                }
            }
        })
        if (arrSeatsFlagged != undefined && arrSeatsFlagged.length > 0 && htmlTarget == "#mapGrilleTarifInner") {
            //calculMaxSeatsToTakeByCateg(htmlTarget)
            calculMaxSeatsToTake(htmlTarget, addCartWrapper, true)
        } else {
            calculMaxSeatsToTake(htmlTarget, addCartWrapper)
        }

        //comptage des places prises
        countAllPlacesSelected(htmlTarget, addCartWrapper)
        customPackage(htmlTarget, addCartWrapper)
        sendIframeSize()
        //gestion du disable/enable sur les bouton - et + des tarifs
        $.each($(htmlTarget + ' .customNumberInput[type="number"]'), function (i, k) {
            if (!$(k).is("[readonly]") && !$(this).is(":disabled")) {
                if (parseInt($(k).val()) >= parseInt($(k).prop("max"))) {
                    $(k).next().find('.btn-increment').prop('disabled', true).addClass('disabled')
                } else {
                    $(k).next().find('.btn-increment').prop('disabled', false).removeClass('disabled')
                }

                if (parseInt($(k).val()) <= parseInt($(k).prop("min"))) {
                    $(k).next().find('.btn-decrement').prop('disabled', true).addClass('disabled')
                } else {
                    $(k).next().find('.btn-decrement').prop('disabled', false).removeClass('disabled')
                }
            }
        })

    });


    //si une préselection existe, on force les valeurs des tarifs
    if (basketPreSelection != undefined && basketPreSelection.length > 0) {
        $.each(basketPreSelection, function (i, k) {
            var thisCategId = k.CategId
            $.each(k.PropertiesSeatSelectionToFlagList, function (il, kl) {
                var thiscustomNumberInput = $(htmlTarget + " .customNumberInput[data-gpid='" + kl.GestionPlaceId + "'][data-priceid='" + kl.PriceId + "'][data-categid='" + thisCategId + "']")
                var thiscustomNumberInputMax = parseInt($(thiscustomNumberInput).attr('max'))
                var thiscustomNumberInputMin = parseInt($(thiscustomNumberInput).attr('min'))
                var thiscustomNumberInputSeatCount = kl.SeatCount
                if (kl.SeatCount > thiscustomNumberInputMax) {
                    thiscustomNumberInputSeatCount = thiscustomNumberInputMax
                }
                if (kl.SeatCount < thiscustomNumberInputMin) {
                    thiscustomNumberInputSeatCount = thiscustomNumberInputMin
                }
                $(thiscustomNumberInput).val(thiscustomNumberInputSeatCount)
            })
        })
    }
    //trigger des inputs des tarifs pour initliser le calcul du nombre de places + montant total
    $(htmlTarget + ' .customNumberInput').trigger('change')

    SwitchToIndex('#calendarCategsPricesWrapper', '#categsAndPricesWrapper', 300)
    
    if (openCategId != undefined) {
        //Si openCategId est renseigné, alors on force l'ouverture sur cette catégorie (ex.: quand on vient du pano)
        setTimeout(function () { $(htmlTarget + " .categWrapper[data-categid='" + openCategId + "'] .tarifLignes").collapse("show") }, 400);
    } else if (SettingsMerge.sessions.forceCloseAllCategories && $(htmlTarget + " .categWrapper").length > 1) {
        //Si l'option appseting forceCloseAllCategories == true et au moins 2 catégories, alors on laisse fermer toutes les catégories

    } else if (SettingsMerge.sessions.forceOpenAllCategories) {
         //Si l'option appseting forceOpenAllCategories == true, alors on ouvre toutes les catégories
        setTimeout(function () { $(htmlTarget + " .categWrapper .tarifLignes").collapse("show") }, 400);
    } else if (SettingsMerge.sessions.forceOpenCategoryIds.length > 0) {
        //Si l'option appseting forceOpenAllCategories == true, alors on ouvre toutes les catégories
        $.each(SettingsMerge.sessions.forceOpenCategoryIds, function (iid, kid) {
            setTimeout(function () { $(htmlTarget + " .categWrapper[data-categid='" + kid + "'] .tarifLignes").collapse("show") }, 400);
        })
        
    } else {
        //Sinon on ouvre la premiere categorie
        setTimeout(function () { $(htmlTarget + " .categWrapper:first .tarifLignes").collapse("show") }, 400);
    }
    sendIframeSize();

    initChangeModalAndCollapse()
}

//il y a-t-il des tarif avantage dans la selection
function selectionHasPricesAvantage(BasketSelection) {
    var isBasketSelectionContainsAvantage = false
    $.each(BasketSelection, function (i, k) {
        var myobj = (k.PropertiesSeatSelectionToFlagList != undefined) ? k.PropertiesSeatSelectionToFlagList : k.propertiesSeatSelectionToFlagList
        $.each(myobj, function (il, kl) {
            var myprops = (kl.Sponsor != undefined && kl.Sponsor.SponsorCode != undefined) ? kl.Sponsor.SponsorCode : kl.sponsor.sponsorCode
            if (myprops.toLowerCase() == "carteavantage") {
                isBasketSelectionContainsAvantage = true
            }
        })
    })
    return isBasketSelectionContainsAvantage;
}



//ouvre la modal tarifs avantage
function openModalPricesAdvantage(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection) {
    $.ajax({
        type: "POST",
        url: widgetCustomerUrl + "GetAdvantageCardFormAjax/" + structureId + "/" + identityId + "/" + eventId + "/" + sessionId + "/" + langCode + "/" + webUserId + "/" + buyerProfilId,
        data: {
            propertiesSeatsSelectionLocationsToFlag: BasketSelection,
            token: partnerToken
        }, success: function (data) {
            $('#modalLinkAvantageCardToPrices .modal-body').html(data)
            pushAttrModalHeightToBody("#modalLinkAvantageCardToPrices")
            sendIframeSize();
        },
        error: function (a, b, c) {
            console.log("GetAdvantageCardFormAjax -> Error")
            console.log(a.responseText)
            $('#modalLinkAvantageCardToPrices .modal-body').html(a.responseText);
            pushAttrModalHeightToBody("#modalLinkAvantageCardToPrices")
            sendIframeSize();
        },
        complete: function () {
            loadingButtonBootstrapOff($("#resumeAddShoppingCartAUTO .addPlaces"))
            loadingButtonBootstrapOff($("#resumeAddShoppingCartPLAN .addPlaces"))
            $('#modalLinkAvantageCardToPrices').modal({
                backdrop: 'static',
                keyboard: false
            })

        }
    });
}

//il y a-t-il des tarif feedbook dans la selection
function selectionHasPricesFeedBook(BasketSelection) {
    var isBasketSelectionContainsFeedBook = false
    $.each(BasketSelection, function (i, k) {
        var myobj = (k.PropertiesSeatSelectionToFlagList != undefined) ? k.PropertiesSeatSelectionToFlagList : k.propertiesSeatSelectionToFlagList
        $.each(myobj, function (il, kl) {
            if (kl.NeedFeedBook) {
                isBasketSelectionContainsFeedBook = true
            }
        })
    })
    return isBasketSelectionContainsFeedBook;
}

//ouvre la modal tarifs avantage
function openModalPricesFeedBook(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection) {
    $.ajax({
        type: "POST",
        url: widgetOfferUrl + "GetFeedBookFormAjax/" + structureId + "/" + identityId + "/" + eventId + "/" + sessionId + "/" + langCode + "/" + webUserId + "/" + buyerProfilId,
        data: {
            propertiesSeatsSelectionLocationsToFlag: BasketSelection,
            token: partnerToken
        }, success: function (data) {
            $('#modalLinkFeedBookToPrices .modal-body').html(data)
            pushAttrModalHeightToBody("#modalLinkFeedBookToPrices")
            sendIframeSize();
        },
        error: function (a, b, c) {
            console.log("GetFeedBookFormAjax -> Error")
            console.log(a.responseText)
            $('#modalLinkFeedBookToPrices .modal-body').html(a.responseText);
            pushAttrModalHeightToBody("#modalLinkFeedBookToPrices")
            sendIframeSize();
        },
        complete: function () {
            loadingButtonBootstrapOff($("#resumeAddShoppingCartAUTO .addPlaces"))
            loadingButtonBootstrapOff($("#resumeAddShoppingCartPLAN .addPlaces"))
            $('#modalLinkFeedBookToPrices').modal({
                backdrop: 'static',
                keyboard: false
            })

        }
    });
}


//il y a-t-il des tarif adhesion dans la selection
function selectionHasPricesAdhesion(BasketSelection) {
    var isBasketSelectionContainsAdhesion = false
    $.each(BasketSelection, function (i, k) {
        var myobj = (k.PropertiesSeatSelectionToFlagList != undefined) ? k.PropertiesSeatSelectionToFlagList : k.propertiesSeatSelectionToFlagList
        $.each(myobj, function (il, kl) {
            var myprops = (kl.IsAdhesion != undefined) ? kl.IsAdhesion : kl.isAdhesion
            if (myprops == true) {
                isBasketSelectionContainsAdhesion = true
            }
        })
    })
    return isBasketSelectionContainsAdhesion;
}
//decide quelle modal ouvrir lorsque la selection de l'utilisateur contient des tarif adhesion (soit demande de login, soit lié les consommateurs)
function openModalAdhesionSwitch(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection) {
    if (identityId != 0) {

        //si l'utilisateur a une identité, on ouvre la sélection des consommateurs
        openModalLinkConsumersToPrices(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection)
    } else {
        //si l'utilisateur n'a pas d'identité, on ouvre la demande de login/inscription
        openModalLoginBecomeAdherant(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection)
    }
}

//ouvre la modal de selection des consommateurs a lié aux tarifs adhésion
function openModalLinkConsumersToPrices(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection) {
    $.ajax({
        type: "POST",
        url: widgetCustomerUrl + "GetAdhesionPriceConsumerAjax/" + structureId + "/" + identityId + "/" + eventId + "/" + sessionId + "/" + langCode + "/" + webUserId + "/" + buyerProfilId,
        data: {
            propertiesSeatsSelectionLocationsToFlag: BasketSelection,
            token: partnerToken
        }, success: function (data) {
            $('#modalLinkConsumersToPrices .modal-body').html(data)
            pushAttrModalHeightToBody("#modalLinkConsumersToPrices")
            $('#modalLinkConsumersToPrices').modal({
                backdrop: 'static',
                keyboard: false
            })
            sendIframeSize();
        },
        error: function (a, b, c) {
            console.log("GetAdhesionPriceConsumerAjax -> Error")
            console.log(a.responseText)
            $('#modalLinkConsumersToPrices .modal-body').html(a.responseText);
            pushAttrModalHeightToBody("#modalLinkConsumersToPrices")
            sendIframeSize();
        },
        complete: function () {
            loadingButtonBootstrapOff($("#resumeAddShoppingCartAUTO .addPlaces"))
            loadingButtonBootstrapOff($("#resumeAddShoppingCartPLAN .addPlaces"))


        }
    });
}

//ouvre la modal de demande de login pour pouvoir bénéficier d'une carte adhesion
function openModalLoginBecomeAdherant(structureId, eventId, sessionId, identityId, webUserId, buyerProfilId, langCode, partnerToken, BasketSelection) {
    //on conserve l'objet en sessionStorage de sa sélection dans le cas ou l'utilisateur se logge ( ce qui implique un refresh de page )
    sessionStorage.setItem("SeatsAutoBasketSelection_" + structureId + "_" + eventId + "_" + sessionId, JSON.stringify(BasketSelection));

    $('#modalLoginBecomeAdherant').modal({
        backdrop: 'static',
        keyboard: false
    })
    //si l'utilisateur clique sur annuler, on supprime alors l'objet du sessionStorage
    $('#modalLoginBecomeAdherant [data-dismiss="modal"]').off('click').on('click', function () {
        sessionStorage.removeItem("SeatsAutoBasketSelection_" + structureId + "_" + eventId + "_" + sessionId);
    })
    $('#modalLoginBecomeAdherantBtnCreateAccount, #modalLoginBecomeAdherantBtnLogin').off('click').on('click', function () {
        window.parent.postMessage({
            "action": "openCustomerArea",
        }, "*")
    })
    loadingButtonBootstrapOff($("#resumeAddShoppingCartAUTO .addPlaces"))
    loadingButtonBootstrapOff($("#resumeAddShoppingCartPLAN .addPlaces"))
}

//si on a chargé la grille de tarif via le plan, on calcul combien de place l'utilisateur a le droit de répartir
/*function calculMaxSeatsToTakeByCateg(htmlTarget) {
    $.each($(htmlTarget + ' .categWrapper'), function (i, k) {
        calculMaxSeatsToTake(k, true)
    })

    //console.log('calculMaxSeatsToTakeByCateg finished')
}*/
//on calcul combien de place l'utilisateur a le droit de répartir sur la grille de tarif (soit via plan -> le nombre de siege pris via le plan, soit en auto -> le nombre max par seance )
function calculMaxSeatsToTake(wrapper, addCartWrapper, fromplan) {
    var fromplan = (fromplan == true) ? fromplan : false;
    var categDispo = 0
    //console.log(addCartWrapper) 
    

    //on calcule combien de places ont déjà été prise par offre et on update les restes à prendre
    $.each(motherRuleGps, function (ri, rk) {
        var totalSeatCount = 0
        var motherRuleGpId = rk.gpid
        var motherRuleMin = rk.min
        var motherRuleMax = rk.max
        var motherRuleRest = rk.rest
        $.each($(wrapper).find(".offerGroup[data-motherrule-gpid=" + motherRuleGpId + "]"), function (iog, kog) {
            $.each($(kog).find(' .customNumberInput[type="number"]'), function (ii, ki) {
                totalSeatCount += parseInt($(ki).val())
            })
        })
        $(wrapper).find(".offerGroup[data-motherrule-gpid=" + motherRuleGpId + "]").attr("data-motherrule-rest", (motherRuleMax - totalSeatCount))
        motherRuleRest = parseInt($(wrapper).find(".offerGroup[data-motherrule-gpid=" + motherRuleGpId + "]").attr("data-motherrule-rest"))
    })
    //////////////////////
    $.each($(wrapper).find('.offerGroup'), function (iog, kog) {

        var motherRuleGpId = 0;
        if ($(kog).attr("data-motherrule-gpid") != '')
            motherRuleGpId = parseInt($(kog).attr("data-motherrule-gpid"));

        var motherRuleMin = 0;
        if ($(kog).attr("data-motherrule-min") != '')
           motherRuleMin =  parseInt($(kog).attr("data-motherrule-min"))

        var motherRuleMax = 0;
        if ($(kog).attr("data-motherrule-max") != '')
            motherRuleMax = parseInt($(kog).attr("data-motherrule-max"))

        var motherRuleRest = 0;
        if ($(kog).attr("data-motherrule-rest") != '')
            motherRuleRest = parseInt($(kog).attr("data-motherrule-rest"))

        //on calcule le minimum de places requises pour chaque offres pour qu'elles soient éligibles (hors offre 0 = tarifs normaux), si le nombre de place restantes est supérieur au minimum de l'offre, on désactive ses tarifs
        var minSeatToBeAvailable = 0
        categDispo = parseInt($(kog).closest('.categWrapper').attr('data-dispo'))
        $.each($(kog).find(' .customNumberInput[type="number"]'), function (ii, ki) {
            if ($(ki).attr('data-ismaster') != undefined && $(ki).attr('data-ismaster') == "True") {
                //tarif maitre
                minSeatToBeAvailable += parseInt($(ki).attr('data-realmin'))
            } else if ($(ki).attr('data-ischildren') != undefined && $(ki).attr('data-ischildren') == "True") {
                //tarif élève
                if ($(ki).attr('data-rodmin') > 0) {
                    minSeatToBeAvailable += parseInt($(ki).attr('data-rodmin')) + 1
                }
            } else {
                //pour les tarifs normaux (sans offre), on désactive les tarifs que l'utilisateur ne peut plus prendre selon le motherRuleRest (le reste de place a prendre par categ)
                $(ki).attr('data-minSeatToBeAvailable', parseInt($(ki).attr('min')))
                if ((motherRuleRest < parseInt($(ki).attr('data-realmin')) || motherRuleRest == 0) && $(ki).val() == 0) {
                    $(ki).prop('disabled', true).prop('readonly', true).closest('.tarifLigne').removeClass('activated')
                    $(ki).next().find('.customNumberInput').prop('disabled', true).prop('readonly', true)
                } else {
                    $(ki).prop('disabled', false).prop('readonly', false).closest('.tarifLigne').addClass('activated')
                    $(ki).next().find('.customNumberInput').prop('disabled', false).prop('readonly', false)
                }
            }

            if ($(ki).attr('data-realmax') == undefined) {
                $(ki).attr('data-realmax', $(ki).attr('max'))
            }

            if ($(ki).attr('data-ratiocalculatedmax') != undefined && $(ki).attr('data-ratiocalculatedmax') <= $(ki).attr('data-realmax')) {
                
                //si c'est un tarif enfant
                if ((parseInt($(ki).val()) + motherRuleRest) < $(ki).attr('data-ratiocalculatedmax')) {
                    //et qu'il lui reste moins de places que ce qu'il a le droit de prendre (ratiocalculatedmax), alors son max devient ce reste
                    $(ki).attr('max', (parseInt($(ki).val()) + motherRuleRest))
                } else {
                    //sinon son max reste ratiocalculatedmax
                    $(ki).attr('max', $(ki).attr('data-ratiocalculatedmax'))
                }
            } else {
                //si c'est un tarif maitre, ou normal
                if ((parseInt($(ki).val()) + motherRuleRest) < $(ki).attr('data-realmax')) {
                    //et qu'il lui reste moins de places que ce qu'il a le droit de prendre (realmax), alors son max devient ce reste
                    $(ki).attr('max', (parseInt($(ki).val()) + motherRuleRest))
                } else {
                    //sinon son max reste realmax
                    $(ki).attr('max', $(ki).attr('data-realmax'))
                }
            }

            $(ki).attr('data-maxseatfromplantotake', $(ki).attr('max'))

        })

        //pour les offres, on désactive les tarifs que l'utilisateur ne peut plus prendre selon le motherRuleRest (le reste de place a prendre par offre) et la dispo
        if ($(kog).attr('data-offerid') != 0) {
            $(kog).attr('data-minSeatToBeAvailable', minSeatToBeAvailable)
            if ((categDispo < minSeatToBeAvailable || motherRuleRest < minSeatToBeAvailable || motherRuleRest == 0) && $(kog).find(' .customNumberInput[type="number"][data-ismaster="True"]').val() == 0) {
                $(kog).addClass('notavailable')
                $(kog).find(' .customNumberInput[type="number"][data-ismaster="True"]').prop('disabled', true).prop('readonly', true).closest('.tarifLigne').removeClass('activated')
            } else {
                $(kog).removeClass('notavailable')
                $(kog).find(' .customNumberInput[type="number"][data-ismaster="True"]').prop('disabled', false).prop('readonly', false).closest('.tarifLigne').addClass('activated')
            }
        } else {

        }
    })
    //Gestion du reste à prendre par categ via le plan
    if (fromplan) {
        $.each($(wrapper).find('.categWrapper'), function (cwi, cwk) {
            var categMaxPlaces = 0
            var categRestPlaces = 0
            
            var totalSeatCountFromPlan = 0
            $.each($(cwk).find('.customNumberInput[type="number"]'), function (ii, ki) {
                totalSeatCountFromPlan += parseInt($(ki).val())
            })
            
            categMaxPlaces = parseInt($(cwk).find('.seatsByCategoryWrapper').attr('data-maxplaces'))
            $(cwk).find('.seatsByCategoryWrapper').attr('data-restplaces', (categMaxPlaces - totalSeatCountFromPlan))
            categRestPlaces = parseInt($(cwk).find('.seatsByCategoryWrapper').attr('data-restplaces'))
            var restseattxt = ((GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeatsToDistribute") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeatsToDistribute") : '{SeatsNumber} place(s) restante(s) à distribuer').replace("{SeatsNumber}", "<strong class='badge'>" + categRestPlaces + "</strong>")
            $(cwk).find('.seatsByCategoryWrapper .seatsByCategory').html(restseattxt)
            $.each($(cwk).find('.offerGroup'), function (iog, kog) {
                var motherRuleRest = parseInt($(kog).attr("data-motherrule-rest"))

                if (motherRuleRest < categRestPlaces) {
                    categRestPlaces=motherRuleRest
                }
                $.each($(kog).find('.customNumberInput[type="number"]'), function (ii, ki) {
                    //fix CAS-99374-J8R8N7
                    //pour les tarifs normaux (sans offre), on désactive les tarifs que l'utilisateur ne peut plus prendre selon le categRestPlaces (le reste de place a prendre par categ)
                    if ($(kog).attr('data-offerid') == 0) {
                        if ((categRestPlaces < parseInt($(ki).attr('data-realmin')) || categRestPlaces == 0) && $(ki).val() == 0) {
                            $(ki).prop('disabled', true).prop('readonly', true).closest('.tarifLigne').removeClass('activated')
                            $(ki).next().find('.customNumberInput').prop('disabled', true).prop('readonly', true)
                        } else {
                            $(ki).prop('disabled', false).prop('readonly', false).closest('.tarifLigne').addClass('activated')
                            $(ki).next().find('.customNumberInput').prop('disabled', false).prop('readonly', false)
                        }
                    }

                    if ($(ki).attr('data-ratiocalculatedmax') != undefined && $(ki).attr('data-ratiocalculatedmax') <= $(ki).attr('data-realmax')) {

                        //si c'est un tarif enfant
                        if ((parseInt($(ki).val()) + categRestPlaces) < $(ki).attr('data-ratiocalculatedmax')) {
                            //et qu'il lui reste moins de places que ce qu'il a le droit de prendre (ratiocalculatedmax), alors son max devient ce reste
                            $(ki).attr('max', (parseInt($(ki).val()) + categRestPlaces))
                        } else {
                            //sinon son max reste ratiocalculatedmax
                            $(ki).attr('max', $(ki).attr('data-ratiocalculatedmax'))
                        }
                    } else {
                        //si c'est un tarif maitre, ou normal
                        if ((parseInt($(ki).val()) + categRestPlaces) < $(ki).attr('data-realmax')) {
                            //et qu'il lui reste moins de places que ce qu'il a le droit de prendre (realmax), alors son max devient ce reste
                            $(ki).attr('max', (parseInt($(ki).val()) + categRestPlaces))
                        } else {
                            //sinon son max reste realmax
                            $(ki).attr('max', $(ki).attr('data-realmax'))
                        }
                    }
                })
            })
            
        })
    }
}
//on calcul combien de place l'utilisateur a le droit de répartir sur la grille de tarif (soit via plan -> le nombre de siege pris via le plan, soit en auto -> le nombre max par seance )
function calculMaxSeatsToTake_OLD(k, fromplan) {
    var fromplan = (fromplan == true) ? fromplan : false;
    //on calcule combien de places ont déjà été prise, et on affiche le reste en dessous de chaque categ
    var totalSeatCount = 0
    $.each($(k).find(' .customNumberInput[type="number"]'), function (ii, ki) {
        totalSeatCount += parseInt($(ki).val())
    })
    var categMaxPlaces = 0
    var categRestPlaces = 0
    var categDispo = 0
    if (fromplan) {
        //sur plan
        categMaxPlaces = parseInt($(k).find('.seatsByCategoryWrapper').attr('data-maxplaces'))
        $(k).find('.seatsByCategoryWrapper').attr('data-restplaces', (categMaxPlaces - totalSeatCount))
        categRestPlaces = parseInt($(k).find('.seatsByCategoryWrapper').attr('data-restplaces'))

        var restseattxt = ((GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeatsToDistribute") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeatsToDistribute") : '{SeatsNumber} place(s) restante(s) à distribuer').replace("{SeatsNumber}", "<strong class='badge'>" + categRestPlaces + "</strong>")
        $(k).find('.seatsByCategoryWrapper .seatsByCategory').html(restseattxt)
    } else {
        //auto
        categMaxPlaces = parseInt($(k).attr('data-maxplaces'))
        $(k).attr('data-restplaces', (categMaxPlaces - totalSeatCount))
        categRestPlaces = parseInt($(k).attr('data-restplaces'))
    }
    $.each($(k).find('.offerGroup'), function (iog, kog) {
        //on calcule le minimum de places requises pour chaque offres (hors offre 0 = tarifs normaux), si le nombre de place restantes est supérieur au minimum de l'offre, on désactive ses tarifs
        var minSeatToBeAvailable = 0
        categDispo = parseInt($(kog).closest('.categWrapper').attr('data-dispo'))
        $.each($(kog).find(' .customNumberInput[type="number"]'), function (ii, ki) {
            if ($(ki).attr('data-ismaster') != undefined && $(ki).attr('data-ismaster') == "True") {
                //tarif maitre
                minSeatToBeAvailable += parseInt($(ki).attr('data-realmin'))
            } else if ($(ki).attr('data-ischildren') != undefined && $(ki).attr('data-ischildren') == "True") {
                //tarif élève
                if ($(ki).attr('data-rodmin') > 0) {
                    minSeatToBeAvailable += parseInt($(ki).attr('data-rodmin')) + 1
                }
            } else {
                //pour les tarifs normaux (sans offre), on désactive les tarifs que l'utilisateur ne peut plus prendre selon le categRestPlaces (le reste de place a prendre par categ)
                $(ki).attr('data-minSeatToBeAvailable', parseInt($(ki).attr('min')))
                if ((categRestPlaces < parseInt($(ki).attr('data-realmin')) || categRestPlaces == 0) && $(ki).val() == 0) {
                    $(ki).prop('disabled', true).prop('readonly', true).closest('.tarifLigne').removeClass('activated')
                    $(ki).next().find('.customNumberInput').prop('disabled', true).prop('readonly', true)
                } else {
                    $(ki).prop('disabled', false).prop('readonly', false).closest('.tarifLigne').addClass('activated')
                    $(ki).next().find('.customNumberInput').prop('disabled', false).prop('readonly', false)
                }
            }

            if ($(ki).attr('data-realmax') == undefined) {
                $(ki).attr('data-realmax', $(ki).attr('max'))
            }

            if ($(ki).attr('data-ratiocalculatedmax') != undefined && $(ki).attr('data-ratiocalculatedmax') <= $(ki).attr('data-realmax')) {
                //si c'est un tarif enfant
                if ((parseInt($(ki).val()) + categRestPlaces) < $(ki).attr('data-ratiocalculatedmax')) {
                    //et qu'il lui reste moins de places que ce qu'il a le droit de prendre (ratiocalculatedmax), alors son max devient ce reste
                    $(ki).attr('max', (parseInt($(ki).val()) + categRestPlaces))
                } else {
                    //sinon son max reste ratiocalculatedmax
                    $(ki).attr('max', $(ki).attr('data-ratiocalculatedmax'))
                }
            } else {
                //si c'est un tarif maitre, ou normal
                if ((parseInt($(ki).val()) + categRestPlaces) < $(ki).attr('data-realmax')) {
                    //et qu'il lui reste moins de places que ce qu'il a le droit de prendre (realmax), alors son max devient ce reste
                    $(ki).attr('max', (parseInt($(ki).val()) + categRestPlaces))
                } else {
                    //sinon son max reste realmax
                    $(ki).attr('max', $(ki).attr('data-realmax'))
                }
            }

            $(ki).attr('data-maxseatfromplantotake', $(ki).attr('max'))

        })

        //pour les offres, on désactive les tarifs que l'utilisateur ne peut plus prendre selon le categRestPlaces (le reste de place a prendre par categ) et la dispo
        if ($(kog).attr('data-offerid') != 0) {
            $(kog).attr('data-minSeatToBeAvailable', minSeatToBeAvailable)
            if ((categDispo < minSeatToBeAvailable || categRestPlaces < minSeatToBeAvailable || categRestPlaces == 0) && $(kog).find(' .customNumberInput[type="number"][data-ismaster="True"]').val() == 0) {
                $(kog).addClass('notavailable')
                $(kog).find(' .customNumberInput[type="number"][data-ismaster="True"]').prop('disabled', true).prop('readonly', true).closest('.tarifLigne').removeClass('activated')
            } else {
                $(kog).removeClass('notavailable')
                $(kog).find(' .customNumberInput[type="number"][data-ismaster="True"]').prop('disabled', false).prop('readonly', false).closest('.tarifLigne').addClass('activated')
            }
        }
    })
}

// charge le VOIR SUR PLAN
function loadSeePlan(sessionId, target) {
    if (target == undefined) {
        target = ".SeePlanSalleGrilleTarif"
    }
    listGpiDs = []
    if (typeof thisSessionLoaded !== "undefined" && thisSessionLoaded.gestionPlaces != null) {
        $.each(thisSessionLoaded.gestionPlaces, function (i, k) {
            listGpiDs.push(k.gestionPlaceId)
        })
    // fix si prise sur plan uniquement
    } else if (typeof thisSessionLoadedPlan !== "undefined" && thisSessionLoadedPlan.gestionPlaces != null) {
        $.each(thisSessionLoadedPlan.gestionPlaces, function (i, k) {
            listGpiDs.push(k.gestionPlaceId)
        })
    }
    // fix si prise sur plan uniquement
    if (typeof listCategs === "undefined") {
        var listCategs = []
    }
    var urlSeatPlan = widgetCatalogUrl + "SeatsPlan/ImageSeatsPlanAjax/" + structureId + "/" + langCode + "/" + eventId + "/" + sessionId + "/" + identityId + "/" + webUserId + "/" + buyerProfilId + "/0/0/0";
    $.ajax({
        type: "GET",
        url: urlSeatPlan,
        data: {
            token: partnerToken,
            ctg: listCategs,
            gpId: listGpiDs
        },
        traditional: true,
        success: function (data) {
            $(target).html(data);
            $(target + '_Wrapper').show()
            $.each($(target+' [data-pricetoformat]'), function (i, k) {
                $(k).html(SetDeviseCode(parseInt($(k).attr('data-pricetoformat'))))
            })
            sendIframeSize()
            initChangeModalAndCollapse()
        },
        error: function (a, b, c) {
            console.log("ImageSeatsPlanAjax -> Error")
            $(target).html(a.responseText);
        }
    });
}
//creer un objet panier temporaire
function CreateBasketSelection(htmlTarget, tab) {
    var arrayFlags = [];
    var arrSeatsFlaggedCloned = []
    if (arrSeatsFlagged != undefined && arrSeatsFlagged.length > 0 && tab == "plan") {
        arrSeatsFlaggedCloned = arrSeatsFlagged.slice();
    }
    $.each($(htmlTarget + ' .tarifLigne'), function (i, k) {
        var SeatCount = parseInt($(k).find('.customNumberInput').val()) || 0
        if (SeatCount > 0) {
            //on verifie si une ligne de l'objet contient deja cette categid
            var objectToFlagByCategId = $.grep(arrayFlags, function (a) {
                return a.CategId == $(k).find('.customNumberInput').attr('data-categid');
            });

            if (objectToFlagByCategId.length > 0) {
                //si il y a un element existant avec la même categid que cette ligne, on push les nouvelles places
                //console.log(objectToFlagByCategId)
                var PropertiesSeatSelectionToFlagObj = {}
                PropertiesSeatSelectionToFlagObj.SeatCount = SeatCount;
                PropertiesSeatSelectionToFlagObj.SeatsId = [];
                if (arrSeatsFlagged != undefined && arrSeatsFlagged.length > 0 && tab == "plan") {
                    for (let i = 0; i < SeatCount; i++) {
                        var indexToInsert = arrSeatsFlaggedCloned.findIndex(x => x.categid === objectToFlagByCategId[0].CategId);
                        if (indexToInsert > -1) {
                            PropertiesSeatSelectionToFlagObj.SeatsId.push(arrSeatsFlaggedCloned[indexToInsert].seatid)
                            arrSeatsFlaggedCloned.splice(indexToInsert, 1);
                        }
                    }
                }
                PropertiesSeatSelectionToFlagObj.FeedBookTokensUsed = []
                PropertiesSeatSelectionToFlagObj.NeedFeedBook = ($(k).find('.customNumberInput').attr('data-isfeedbook') == 'True') ? true : false;
                PropertiesSeatSelectionToFlagObj.GestionPlaceId = parseInt($(k).find('.customNumberInput').attr('data-gpid'));
                PropertiesSeatSelectionToFlagObj.PriceId = parseInt($(k).find('.customNumberInput').attr('data-priceid'));
                PropertiesSeatSelectionToFlagObj.IsAdhesion = ($(k).find('.customNumberInput').attr('data-isadhesion') == 'True') ? true : false;
                PropertiesSeatSelectionToFlagObj.Sponsor = {}
                PropertiesSeatSelectionToFlagObj.Sponsor.SponsorCode = ($(k).find('.customNumberInput').attr('data-sponsor') != undefined) ? $(k).find('.customNumberInput').attr('data-sponsor') : "";
                PropertiesSeatSelectionToFlagObj.IsMaitre = ($(k).find('.customNumberInput').attr('data-ismaster') == 'True') ? true : false;
                PropertiesSeatSelectionToFlagObj.Price = {};
                PropertiesSeatSelectionToFlagObj.Price.PriceName = $(k).find('.customNumberInput').attr('data-pricename');
                PropertiesSeatSelectionToFlagObj.Price.UnitTTCAmount = $(k).find('.customNumberInput').attr('data-priceamount');
                PropertiesSeatSelectionToFlagObj.ChildrenPricesIdLinked = ($(k).find('.customNumberInput').attr('data-childrenids') != undefined) ? JSON.parse($(k).find('.customNumberInput').attr('data-childrenids')) : [];
                PropertiesSeatSelectionToFlagObj.MasterPricesIdLinked = ($(k).find('.customNumberInput').attr('data-masterids') != undefined) ? JSON.parse($(k).find('.customNumberInput').attr('data-masterids')) : [];
                PropertiesSeatSelectionToFlagObj.ListIdentities = [];
                PropertiesSeatSelectionToFlagObj.OfferId = ($(k).find('.customNumberInput').attr('data-offerid') != undefined) ? parseInt($(k).find('.customNumberInput').attr('data-offerid')) : 0;
                PropertiesSeatSelectionToFlagObj.AdhesionCatalogId = ($(k).find('.customNumberInput').attr('data-catalogadhesionid') != undefined) ? parseInt($(k).find('.customNumberInput').attr('data-catalogadhesionid')) : 0;
                PropertiesSeatSelectionToFlagObj.RodMin = ($(k).find('.customNumberInput').attr('data-rodmin') != undefined) ? parseInt($(k).find('.customNumberInput').attr('data-rodmin')) : 0;
                PropertiesSeatSelectionToFlagObj.RodMax = ($(k).find('.customNumberInput').attr('data-rodmax') != undefined) ? parseInt($(k).find('.customNumberInput').attr('data-rodmax')) : 0;
                objectToFlagByCategId[0].PropertiesSeatSelectionToFlagList.push(PropertiesSeatSelectionToFlagObj);

            } else {
                //sinon on créer un nouvel element avec cette categid
                var PropertiesSeatsSelectionLocationObj = {};
                PropertiesSeatsSelectionLocationObj.NeedFeedBook = $(k).find('.customNumberInput').attr('data-isfeedbook');
                PropertiesSeatsSelectionLocationObj.ZoneId = parseInt($('#zoneSelect').val()) || 0;
                PropertiesSeatsSelectionLocationObj.FloorId = parseInt($('#floorSelect').val()) || 0;
                PropertiesSeatsSelectionLocationObj.SectionId = parseInt($('#sectionSelect').val()) || 0;
                PropertiesSeatsSelectionLocationObj.CategId = parseInt($(k).find('.customNumberInput').attr('data-categid'));
                PropertiesSeatsSelectionLocationObj.CategName = $(k).closest('.categWrapper').find('.categorieName').text();
                PropertiesSeatsSelectionLocationObj.PropertiesSeatSelectionToFlagList = [];
                var PropertiesSeatSelectionToFlagObj = {}
                PropertiesSeatSelectionToFlagObj.SeatCount = SeatCount;
                PropertiesSeatSelectionToFlagObj.SeatsId = [];
                if (arrSeatsFlagged != undefined && arrSeatsFlagged.length > 0 && tab == "plan") {
                    for (let i = 0; i < SeatCount; i++) {
                        var indexToInsert = arrSeatsFlaggedCloned.findIndex(x => x.categid === PropertiesSeatsSelectionLocationObj.CategId);
                        if (indexToInsert > -1) {
                            PropertiesSeatSelectionToFlagObj.SeatsId.push(arrSeatsFlaggedCloned[indexToInsert].seatid)
                            arrSeatsFlaggedCloned.splice(indexToInsert, 1);
                        }
                    }
                }

                PropertiesSeatSelectionToFlagObj.FeedBookTokensUsed = []
                PropertiesSeatSelectionToFlagObj.NeedFeedBook = ($(k).find('.customNumberInput').attr('data-isfeedbook') == 'True') ? true : false;
                PropertiesSeatSelectionToFlagObj.GestionPlaceId = parseInt($(k).find('.customNumberInput').attr('data-gpid'));
                PropertiesSeatSelectionToFlagObj.PriceId = parseInt($(k).find('.customNumberInput').attr('data-priceid'));
                PropertiesSeatSelectionToFlagObj.IsAdhesion = ($(k).find('.customNumberInput').attr('data-isadhesion') == 'True') ? true : false;
                PropertiesSeatSelectionToFlagObj.Sponsor = {}
                PropertiesSeatSelectionToFlagObj.Sponsor.SponsorCode = ($(k).find('.customNumberInput').attr('data-sponsor') != undefined) ? $(k).find('.customNumberInput').attr('data-sponsor') : "";
                PropertiesSeatSelectionToFlagObj.IsMaitre = ($(k).find('.customNumberInput').attr('data-ismaster') == 'True') ? true : false;
                PropertiesSeatSelectionToFlagObj.Price = {};
                PropertiesSeatSelectionToFlagObj.Price.PriceName = $(k).find('.customNumberInput').attr('data-pricename');
                PropertiesSeatSelectionToFlagObj.Price.UnitTTCAmount = $(k).find('.customNumberInput').attr('data-priceamount');
                PropertiesSeatSelectionToFlagObj.ChildrenPricesIdLinked = ($(k).find('.customNumberInput').attr('data-childrenids') != undefined) ? JSON.parse($(k).find('.customNumberInput').attr('data-childrenids')) : [];
                PropertiesSeatSelectionToFlagObj.MasterPricesIdLinked = ($(k).find('.customNumberInput').attr('data-masterids') != undefined) ? JSON.parse($(k).find('.customNumberInput').attr('data-masterids')) : [];
                PropertiesSeatSelectionToFlagObj.ListIdentities = [];
                PropertiesSeatSelectionToFlagObj.OfferId = ($(k).find('.customNumberInput').attr('data-offerid') != undefined) ? parseInt($(k).find('.customNumberInput').attr('data-offerid')) : 0;
                PropertiesSeatSelectionToFlagObj.AdhesionCatalogId = ($(k).find('.customNumberInput').attr('data-catalogadhesionid') != undefined) ? parseInt($(k).find('.customNumberInput').attr('data-catalogadhesionid')) : 0;
                PropertiesSeatSelectionToFlagObj.RodMin = ($(k).find('.customNumberInput').attr('data-rodmin') != undefined) ? parseInt($(k).find('.customNumberInput').attr('data-rodmin')) : 0;
                PropertiesSeatSelectionToFlagObj.RodMax = ($(k).find('.customNumberInput').attr('data-rodmax') != undefined) ? parseInt($(k).find('.customNumberInput').attr('data-rodmax')) : 0;
                PropertiesSeatsSelectionLocationObj.PropertiesSeatSelectionToFlagList.push(PropertiesSeatSelectionToFlagObj);

                arrayFlags.push(PropertiesSeatsSelectionLocationObj)
            }
        }

    });
    return arrayFlags;
}

//flag et ajoute une sélection de places au panier
function addToBasket(structureId, eventId, sessionId, identityId, basketId, webUserId, buyerProfilId, langCode, token, BasketSelection, origin) {


    loadingButtonBootstrapOn($("#resumeAddShoppingCartAUTO .addPlaces"))
    loadingButtonBootstrapOn($("#resumeAddShoppingCartPLAN .addPlaces"))

    var urlFlag = widgetOfferUrl + structureId + "/FlagAjax/" + eventId + "/" + sessionId + "/" + identityId + "/" + basketId + "/" + webUserId + "/" + buyerProfilId + "/" + langCode;
    $.ajax({
        type: "POST",
        url: urlFlag,
        data: {
            propertiesSeatsSelectionLocationsToFlag: BasketSelection,
            token: token
        },
        success: function (data) {
            /* dernieres places ajoutées */
            var seatsAdded = []
            if (data.seatsAdded != undefined && data.seatsAdded != null) {
                $.each(data.seatsAdded, function (i, k) {
                    seatsAdded.push(k.seatId)
                })
            }
            switch (data.msg) {
                case 'discontigues': /* places non-contigues */
                    $('#modalAddBasketPlacesDiscontigues').modal({
                        backdrop: 'static',
                        keyboard: false
                    })
                    /* annuler -> unflag */
                    $('#modalAddBasketPlacesDiscontiguesBtnCancel').off('click').on('click', function () {
                        unflagSeatGT(data.basket.basketId, seatsAdded, eventId, sessionId)
                        $('#modalAddBasketPlacesDiscontigues').modal('hide')

                        loadingButtonBootstrapOff($("#resumeAddShoppingCartAUTO .addPlaces"))
                        loadingButtonBootstrapOff($("#resumeAddShoppingCartPLAN .addPlaces"))
                    })
                    /* confirm -> gotobasket */
                    $('#modalAddBasketPlacesDiscontiguesConfirm').off('click').on('click', function () {
                        $('#modalAddBasketPlacesDiscontigues').modal('hide')

                        var msg = {
                            "action": "addBasket",
                            "basketId": data.basket.basketId,
                            "hash": data.basket.hash
                        }
                        window.parent.postMessage(msg, '*')
                    })
                    break;
                case 'strapontins': /* places strapontins */
                    $('#modalAddBasketPlacesStrapontins').modal({
                        backdrop: 'static',
                        keyboard: false
                    })
                    /* annuler -> unflag */
                    $('#modalAddBasketPlacesStrapontinsBtnCancel').off('click').on('click', function () {
                        unflagSeatGT(data.basket.basketId, seatsAdded, eventId, sessionId)
                        $('#modalAddBasketPlacesStrapontins').modal('hide')

                        loadingButtonBootstrapOff($("#resumeAddShoppingCartAUTO .addPlaces"))
                        loadingButtonBootstrapOff($("#resumeAddShoppingCartPLAN .addPlaces"))
                    })
                    /* confirm -> gotobasket */
                    $('#modalAddBasketPlacesStrapontinsConfirm').off('click').on('click', function () {
                        $('#modalAddBasketPlacesStrapontins').modal('hide')

                        var msg = {
                            "action": "addBasket",
                            "basketId": data.basket.basketId,
                            "hash": data.basket.hash
                        }
                        window.parent.postMessage(msg, '*')
                    })
                    break;
                case 'discontiguesstrapontins': /* places non-contigues ET strapontins */
                    $('#modalAddBasketPlacesDiscontiguesStrapontins').modal({
                        backdrop: 'static',
                        keyboard: false
                    })
                    /* annuler -> unflag */
                    $('#modalAddBasketPlacesDiscontiguesStrapontinsBtnCancel').off('click').on('click', function () {
                        unflagSeatGT(data.basket.basketId, seatsAdded, eventId, sessionId)
                        $('#modalAddBasketPlacesDiscontiguesStrapontins').modal('hide')

                        loadingButtonBootstrapOff($("#resumeAddShoppingCartAUTO .addPlaces"))
                        loadingButtonBootstrapOff($("#resumeAddShoppingCartPLAN .addPlaces"))
                    })
                    /* confirm -> gotobasket */
                    $('#modalAddBasketPlacesDiscontiguesStrapontinsConfirm').off('click').on('click', function () {
                        $('#modalAddBasketPlacesDiscontiguesStrapontins').modal('hide')

                        var msg = {
                            "action": "addBasket",
                            "basketId": data.basket.basketId,
                            "hash": data.basket.hash
                        }
                        window.parent.postMessage(msg, '*')
                    })
                    break;
                default:
                    // modal avec choix après ajout au panier
                    $('#modalAddBasketChoice').modal({
                        backdrop: 'static',
                        keyboard: false
                    })

                    /* Aller vers le panier */
                    $('#modalAddBasketChoiceGoToBasket').off('click').on('click', function () {
                        $('#modalAddBasketChoice').modal('hide')

                        var msg = {
                            "action": "addBasket",
                            "basketId": data.basket.basketId,
                            "hash": data.basket.hash,
                            "showBasketChoice": true
                        }
                        window.parent.postMessage(msg, '*')
                    })

                    /* Sélectionner une autre manifestation */
                    $('#modalAddBasketChoiceSelectOther').off('click').on('click', function () {
                        $('#modalAddBasketChoice').modal('hide')

                        var msg = {
                            "action": "addBasket",
                            "basketId": data.basket.basketId,
                            "hash": data.basket.hash,
                            "showBasketChoice": false
                        }
                        window.parent.postMessage(msg, '*')
                    })
            }

            

        },
        error: function (a, b, c) {
            if (a.responseJSON.status == 401 && a.responseJSON.title == "priceGrid:RightsOnPricesError") {
                //l'utilisateur n'a plus le droit (offre) de prendre certains tarifs qui se trouvent dans sa sélection
                //on les liste et on lui décrit les tarifs concernés
                var forbiddenGpIds = a.responseJSON.detail.replace('gps:', '').split(',')
                if (forbiddenGpIds.length > 0 && origin == "#modalLinkConsumersToPrices") {
                    forbiddenPriceNames = []
                    $.each(forbiddenGpIds, function (i, k) {
                        forbiddenPriceNames.push($(origin).find('.oneRowPriceAdhesion[data-gpid="' + k + '"] .oneRowPriceAdhesionPriceName').html())
                    })
                    //var forbiddenPriceNamesText = forbiddenPriceNames.map(tarifnames => `<strong>${tarifnames}</strong>`).join(', ');
                    var forbiddenPriceNamesText = $.map(forbiddenPriceNames, function (e) {
                        return '<strong>' + e + '</strong>';
                    }).join(', ');
                    $('#RightsOnPricesError .pricesListRightsOnPricesError').html(forbiddenPriceNamesText)
                    $('#RightsOnPricesError').show()
                    pushAttrModalHeightToBody(origin)
                    sendIframeSize()
                }
            } else if (a.responseJSON.status == 401 && (a.responseJSON.title == "priceGrid:NotEnoughSeatAvailable" || a.responseJSON.title == "jauges:NotEnoughSeatAvailable")) {
                /* plus assez de place disponibes, rien n'est flaggé ou ajouté au panier */
                $('#modalAddBasketNotEnoughSeatAvailable').modal()
            } else {
                console.log("FlagAjax -> Error")
            }

            loadingButtonBootstrapOff($("#resumeAddShoppingCartAUTO .addPlaces"))
            loadingButtonBootstrapOff($("#resumeAddShoppingCartPLAN .addPlaces"))
        },
        complete: function () {
            loadingButtonBootstrapOff($("#addSelectionToBasket"))
        }
    });
}

//unflag des sieges via la grille de tarif
function unflagSeatGT(basketId, seatIds, eventId, sessionId) {
    var url = widgetOfferUrl + structureId + "/unflagajax/" + (basketId || 0) + "/" + eventId + "/" + sessionId + "/" + webUserId + "/" + langCode;
    $.ajax({
        type: "POST",
        url: url,
        data: {
            seatsId: seatIds,
            token: partnerToken
        },
        success: function (data) {
            console.log('unflagSeatGT success')
        },
        error: function (a, b, c) {
            console.log("unflagSeatGT -> Error")
        }
    });
}

// switch + animation entre plusieurs wrapper au sein du même container
function SwitchToIndex(container, selector, timeToTransition, callback) {
    var selectorIndex = $(selector).index()
    var allChildren = $(container).children()
    var pourcentToSwitch = 100 * selectorIndex;

    $(selector).css({ "height": "100%" })

    var getMargin = 0
    $.each(allChildren, function (i, k) {
        var realPourcent = "-" + pourcentToSwitch + "%"
        if (pourcentToSwitch <= 0) {
            realPourcent = pourcentToSwitch + "%"
        }
        var containerGap = (parseInt($(container).css('gap')) || 0) + 'px';
        $(k).css({
            "transition": "transform " + timeToTransition / 1000 + "s ease",
            "transform": "translateX( calc(" + realPourcent + " - " + containerGap + "))"
        })
    })

    setTimeout(function () {
        $(allChildren).not(selector).css({ "height": "0" })
        if (typeof callback === 'function') {
            callback();
        }
    }, timeToTransition);

}/*
function SwitchToIndex(container, selector, timeToTransition, callback) {
    var selectorIndex = $(selector).index()
    var allChildren = $(container).children()
    var pourcentToSwitch = 100 * selectorIndex;
    $(selector).animate({
        'height': '100%'
    }, timeToTransition, function () {
        $(allChildren).not(selector).animate({
            'height': '0'
        }, timeToTransition);
    })

    var getMargin = 0
    $.each(allChildren, function (i, k) {
        var realPourcent = "-" + pourcentToSwitch + "%"
        if (pourcentToSwitch <= 0) {
            realPourcent = pourcentToSwitch + "%"
        }
        $(k).css({
            "transition": "all " + timeToTransition / 1000 + "s ease",
            "transform": "translateX(" + realPourcent + ")"
        })
    })

    if (typeof callback === 'function') {
        setTimeout(function () {
            callback();
        }, timeToTransition);
    }

}
*/
/** Calcul toutes les places selectionné **/
function countAllPlacesSelected(htmlTarget, addCartWrapper) {
    //console.log('countAllPlacesSelected start')
    var countTotalPlaces = 0
    var countTotalPlacesAmount = 0
    var countTotalPlacesSpecialAmount = 0

    var hasTicketAmont = false;
    //en mode NON revendeur, on cherche si parmi les tarifs sélectionné, au moins 1 affiche la valeur
    if (!BuyerProfilInfos.IsReseller) {
        $.each($(htmlTarget + ' .tarifLigne'), function (i, k) {
            if (parseInt($(k).find('.customNumberInput').not('[readonly]').val()) > 0 && parseInt($(k).find('.ticketamount').data('amount')) > 0) {
                hasTicketAmont = true;
            }
        })
    }

    $.each($(htmlTarget + ' .tarifLigne'), function (i, k) {
        var nbPlace = parseInt($(k).find('.customNumberInput').not('[readonly]').val()) || 0
        countTotalPlaces += nbPlace

        var normalPrice = 0;
        var specialPrice = 0;

        if (parseInt($(k).find('.ticketamount').data('amount')) > 0 && BuyerProfilInfos.IsReseller) {
            //si la valeur est supérieur à 0 et que le profil est en mode revendeur, alors le prix principal est la valeur
            normalPrice = parseInt($(k).find('.ticketamount').data('amount'));
        }
        else {
            //la valeur est barré
            if (parseInt($(k).find('.ticketamount').data('amount')) > 0) {
                specialPrice = parseInt($(k).find('.ticketamount').data('amount'));
            } else if (hasTicketAmont) {
                specialPrice = parseInt($(k).find('.amount').data('amount'));
            }

            //le prix principal est le montant normal
            normalPrice = parseInt($(k).find('.amount').data('amount'))
        }

        countTotalPlacesSpecialAmount += nbPlace * specialPrice
        countTotalPlacesAmount += nbPlace * normalPrice
    })
    var txtplace = ((GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") != '') ? GetTranslationTerm(TranslationsList, "Widget_Session_LblXSeats") : '{SeatsNumber} place(s)').replace("{SeatsNumber}", countTotalPlaces)
    if (countTotalPlacesSpecialAmount > 0) {
        $(addCartWrapper + ' .totalPlacesSpecialAmount').html(SetDeviseCode(countTotalPlacesSpecialAmount)).addClass('mr-1')
    } else {
        $(addCartWrapper + ' .totalPlacesSpecialAmount').html("").removeClass('mr-1')
    }
    $(addCartWrapper + ' .totalPlacesAmount').html(SetDeviseCode(countTotalPlacesAmount))

    //on verifie si le min des regles mères sont respectées
    var allMotherRulesOk = true
    $(addCartWrapper + ' .alertMotherRuleMinWrapper').html("")
    $.each(motherRuleGps, function (ri, rk) {
        var totalSeatCount = 0
        var motherRuleGpId = rk.gpid
        var motherRuleMin = rk.min
        var priceNamesArray = [];
        var motherRuleNeedToTake = 0;
        $.each($(htmlTarget).find(".offerGroup[data-motherrule-gpid=" + motherRuleGpId + "]"), function (iog, kog) {
            $.each($(kog).find('.customNumberInput[type="number"]'), function (ii, ki) {
                totalSeatCount += parseInt($(ki).val())
            })
            motherRuleNeedToTake = motherRuleMin - totalSeatCount
            $.each($(kog).find('.tarifLigne .tarifNameInner'), function (tni, tnk) {
                if (!priceNamesArray.includes($(tnk).html())) {

                    priceNamesArray.push($(tnk).html())
                }
            })
        })
        if (totalSeatCount > 0 && totalSeatCount < motherRuleMin) {
            allMotherRulesOk = false;
            if (totalSeatCount > 0 && totalSeatCount < motherRuleMin) {
                var alertMotherRuleMinTemplate = $("#alertMotherRuleMinTemplate").html()
                $(addCartWrapper + ' .alertMotherRuleMinWrapper').append(alertMotherRuleMinTemplate.replace("{PriceNamesList}", priceNamesArray.join(", ")).replace("{SeatsNumber}", motherRuleNeedToTake))
            }
        }
    })

    switch (addCartWrapper) {
        //en auto
        case '#resumeAddShoppingCartAUTO':

            $(addCartWrapper + ' .totalPlacesNumber').html(txtplace)
            if (countTotalPlaces > 0 && allMotherRulesOk) {
                $(addCartWrapper + ' .addPlaces').removeClass('disabled').attr("disabled", false);
            } else {
                $(addCartWrapper + ' .addPlaces').addClass('disabled').attr("disabled", true);
            }
            break;
        //après plan
        case '#resumeAddShoppingCartPLAN':
            var totalCountRestPlaces = 0
            $.each($(htmlTarget + ' .categWrapper'), function (i, k) {
                totalCountRestPlaces += parseInt($(k).find('.seatsByCategoryWrapper').attr('data-maxplaces'))
            })

            $(addCartWrapper + ' .totalPlacesNumber').html(txtplace)
            if (totalCountRestPlaces == countTotalPlaces && allMotherRulesOk) {
                $(addCartWrapper + ' .addPlaces').removeClass('disabled').attr("disabled", false);
            } else {
                $(addCartWrapper + ' .addPlaces').addClass('disabled').attr("disabled", true);
            }
            break
    }



}


//formatage des devises
function formatDevise() {
    $.each($('[data-pricetoformat]'), function (i, k) {
        $(k).html(SetDeviseCode(parseInt($(k).attr('data-pricetoformat'))))
    })
}

//Supression du montant des manifs cartes adhesions
function CacherPrixCarteAdhesion() {
    if ($('span.categorieName:contains("PASS")').length == 1) {
        $("span.amount").hide();
        $.each($('span.tarifNameInner'), function (i, k) {
            priceLabel = $(k).text();
            const myArray = priceLabel.split("- ");
            var tt = myArray[1];
            tt = "<span class='amount' data-amount='0' data-pricetoformat='0'>" + tt + "</span>";
            myArray.splice(1, 1, tt);
            tt = myArray.join(" ");
            $(k).html(tt);
        });
    }
}


//function ModalAfterAddPlaces() {
//    var objectmodal = {
//        modalIdSelector: "ModalAfterAddPlaces",
//        formIdSelector:"",
//        iframeSelector:"#sWOffers_ListSession",
//        htmlContent:"",
//        isConfirmation: false
//    }

//    $.ajax({
//        type: "GET",
//        url: 'https://localhost:44310/Session/modalpanierajax',
        
//        success: function (data) {
//            console.log('unflagSeatGT success')
//            objectmodal.htmlContent = data
//            useCustomModal(objectmodal)
//        },
//        error: function (a, b, c) {
//            console.log("unflagSeatGT -> Error")
//        }
//    });

//    //fetch(`https://localhost:44310/Session/modalpanier/ajax/`)
//    //    .then((result) => {
//    //        console.log(result); objectmodal.htmlContent = result.text
//    //    }) 

    

//    // fetch html modal 
//    // use custom modal 

//}

// Fonction pour corriger l'affichage des éléments .noevent
function fixNoeventElements() {
    $('.noevent').each(function() {
        const $element = $(this);

        // Supprimer tous les styles de bouton
        $element.css({
            'background': 'none',
            'color': '#666',
            'height': 'auto',
            'line-height': '1.4',
            'padding': '10px 0',
            'display': 'block',
            'border-radius': '0',
            'text-align': 'center',
            'white-space': 'normal',
            'word-wrap': 'break-word',
            'cursor': 'default',
            'border': 'none',
            'box-shadow': 'none',
            'outline': 'none'
        });

        // Supprimer les classes de bouton si elles existent
        $element.removeClass('btn btn-primary btn-secondary btn-default');

        // S'assurer que l'élément n'est pas cliquable
        $element.off('click');
        $element.attr('tabindex', '-1');
    });
}

// Observer pour surveiller les changements DOM et appliquer la correction automatiquement
const noeventObserver = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                    if ($(node).hasClass('noevent') || $(node).find('.noevent').length > 0) {
                        setTimeout(fixNoeventElements, 10);
                    }
                }
            });
        }
    });
});

// Démarrer l'observation
if (document.getElementById('alleventhours')) {
    noeventObserver.observe(document.getElementById('alleventhours'), {
        childList: true,
        subtree: true
    });
}
