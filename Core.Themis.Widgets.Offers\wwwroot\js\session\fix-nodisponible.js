// Script ULTRA-AGRESSIF pour corriger l'affichage des messages de non-disponibilité

// Injecter CSS ultra-spécifique pour écraser complètement les styles existants
function injectUltraAggressiveCSS() {
    console.log('🔥 INJECTION CSS ULTRA-AGRESSIVE');

    // Supprimer les anciens styles injectés
    $('#ultra-aggressive-noevent-fix').remove();

    // Créer et injecter le nouveau CSS
    var style = document.createElement('style');
    style.id = 'ultra-aggressive-noevent-fix';
    style.innerHTML = `
        /* SOLUTION ULTRA-AGRESSIVE - ÉCRASER COMPLÈTEMENT LE CSS EXISTANT */

        /* Cibler EXACTEMENT le sélecteur problématique avec spécificité maximale */
        #alleventhours .noevent,
        div#alleventhours span.noevent,
        #alleventhours span.noevent,
        span.noevent,
        .noevent {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            color: #000000 !important;
            border: none !important;
            border-radius: 0px !important;
            box-shadow: none !important;
            outline: none !important;
            height: auto !important;
            line-height: 1.5 !important;
            padding: 15px 10px !important;
            margin: 10px 0 !important;
            display: block !important;
            text-align: center !important;
            white-space: normal !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            cursor: text !important;
            pointer-events: none !important;
            user-select: text !important;
            font-weight: normal !important;
            font-size: 14px !important;
            transition: none !important;
            transform: none !important;
            opacity: 1 !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
            min-height: auto !important;
            max-height: none !important;
            width: auto !important;
        }

        /* Hover states - ÉCRASER TOUS LES HOVERS */
        #alleventhours .noevent:hover,
        div#alleventhours span.noevent:hover,
        #alleventhours span.noevent:hover,
        span.noevent:hover,
        .noevent:hover {
            background: transparent !important;
            background-color: transparent !important;
            color: #000000 !important;
            border-radius: 0px !important;
            box-shadow: none !important;
        }

        /* Classe pour les éléments remplacés */
        .noevent-fixed {
            background: transparent !important;
            color: #000000 !important;
            border: none !important;
            border-radius: 0px !important;
            padding: 15px 10px !important;
            display: block !important;
            text-align: center !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
        }
    `;

    document.head.appendChild(style);
    console.log('✅ CSS ULTRA-AGRESSIF injecté avec succès');
}

// Fonction ULTRA-AGRESSIVE pour corriger les éléments .noevent
function fixNoeventElements() {
    console.log('🔥 ULTRA AGGRESSIVE FIX - Début');

    // Méthode 1: Sélecteurs multiples
    var selectors = [
        '#alleventhours .noevent',
        '.noevent',
        'span.noevent',
        '#alleventhours span.noevent',
        '[class*="noevent"]'
    ];

    selectors.forEach(function(selector) {
        $(selector).each(function() {
            var $el = $(this);
            var text = $el.text().trim();

            if (text && text.length > 10) { // Si c'est bien notre texte
                console.log('🔥 TROUVÉ avec sélecteur "' + selector + '": ' + text.substring(0, 30));

                // REMPLACEMENT IMMÉDIAT par du HTML simple
                var newElement = $('<div class="noevent-fixed" style="' +
                    'background: transparent !important; ' +
                    'background-color: transparent !important; ' +
                    'color: #000000 !important; ' +
                    'border: none !important; ' +
                    'border-radius: 0px !important; ' +
                    'box-shadow: none !important; ' +
                    'padding: 15px 10px !important; ' +
                    'margin: 10px 0 !important; ' +
                    'display: block !important; ' +
                    'text-align: center !important; ' +
                    'font-size: 14px !important; ' +
                    'font-weight: normal !important; ' +
                    'line-height: 1.5 !important; ' +
                    'white-space: normal !important; ' +
                    'word-wrap: break-word !important; ' +
                    'cursor: text !important; ' +
                    'pointer-events: none !important;' +
                    '">' + text + '</div>');

                $el.replaceWith(newElement);
                console.log('✅ REMPLACÉ avec succès !');
            }
        });
    });

    console.log('🔥 ULTRA AGGRESSIVE FIX - Terminé');
}

// CORRECTION IMMÉDIATE ET ULTRA-AGRESSIVE au chargement du script
$(document).ready(function() {
    console.log('🚀 Fix nodisponible: Script ULTRA-AGRESSIF chargé');

    // ÉTAPE 1: Injecter le CSS ultra-agressif IMMÉDIATEMENT
    injectUltraAggressiveCSS();

    // ÉTAPE 2: Appliquer la correction JavaScript IMMÉDIATEMENT
    fixNoeventElements();

    // ÉTAPE 3: Réappliquer plusieurs fois avec des délais différents
    setTimeout(function() {
        injectUltraAggressiveCSS();
        fixNoeventElements();
    }, 50);

    setTimeout(function() {
        injectUltraAggressiveCSS();
        fixNoeventElements();
    }, 200);

    setTimeout(function() {
        injectUltraAggressiveCSS();
        fixNoeventElements();
    }, 500);

    setTimeout(function() {
        injectUltraAggressiveCSS();
        fixNoeventElements();
    }, 1000);

    setTimeout(function() {
        injectUltraAggressiveCSS();
        fixNoeventElements();
    }, 2000);

    // ÉTAPE 4: Observer les changements DOM pour les nouveaux éléments
    if (window.MutationObserver) {
        var observer = new MutationObserver(function(mutations) {
            var shouldReapply = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    $(mutation.addedNodes).each(function() {
                        if ($(this).hasClass && $(this).hasClass('noevent')) {
                            shouldReapply = true;
                        }
                        if ($(this).find && $(this).find('.noevent').length > 0) {
                            shouldReapply = true;
                        }
                    });
                }
            });

            if (shouldReapply) {
                console.log('🔄 Mutation détectée - Réapplication du fix');
                setTimeout(function() {
                    injectUltraAggressiveCSS();
                    fixNoeventElements();
                }, 50);
            }
        });

        var target = document.getElementById('alleventhours');
        if (target) {
            observer.observe(target, { childList: true, subtree: true });
            console.log('👁️ Observer installé sur #alleventhours');
        }

        // Observer aussi le document entier au cas où
        observer.observe(document.body, { childList: true, subtree: true });
        console.log('👁️ Observer installé sur document.body');
    }

    // ÉTAPE 5: Vérification périodique toutes les 3 secondes
    setInterval(function() {
        var elements = $('#alleventhours .noevent, .noevent');
        if (elements.length > 0) {
            console.log('🔄 Vérification périodique - ' + elements.length + ' éléments .noevent trouvés');
            injectUltraAggressiveCSS();
            fixNoeventElements();
        }
    }, 3000);

    console.log('🎯 Fix nodisponible: Configuration ULTRA-AGRESSIVE terminée');
});
