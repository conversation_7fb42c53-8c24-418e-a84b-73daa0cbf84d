// SOLUTION IMMÉDIATE - Suppression complète du bouton .noevent
// Ce script force l'application du style sans attendre la recompilation CSS

(function() {
    'use strict';
    
    console.log('🔧 Fix Noevent Immediate - Chargement...');
    
    // Fonction pour appliquer le style de texte simple IMMÉDIATEMENT
    function forceFixNoeventElements() {
        const elements = document.querySelectorAll('#alleventhours .noevent, .noevent');
        
        console.log('🔧 Fix Noevent: ' + elements.length + ' éléments .noevent trouvés');
        
        elements.forEach(function(element) {
            console.log('🔧 Correction de:', element.textContent.substring(0, 50) + '...');
            
            // FORCER le style de texte simple avec setProperty pour override tout
            const style = element.style;
            style.setProperty('background', 'transparent', 'important');
            style.setProperty('background-color', 'transparent', 'important');
            style.setProperty('background-image', 'none', 'important');
            style.setProperty('color', '#000', 'important');
            style.setProperty('border', 'none', 'important');
            style.setProperty('border-radius', '0', 'important');
            style.setProperty('box-shadow', 'none', 'important');
            style.setProperty('outline', 'none', 'important');
            style.setProperty('height', 'auto', 'important');
            style.setProperty('line-height', '1.5', 'important');
            style.setProperty('padding', '15px 10px', 'important');
            style.setProperty('margin', '10px 0', 'important');
            style.setProperty('display', 'block', 'important');
            style.setProperty('text-align', 'center', 'important');
            style.setProperty('white-space', 'normal', 'important');
            style.setProperty('word-wrap', 'break-word', 'important');
            style.setProperty('overflow-wrap', 'break-word', 'important');
            style.setProperty('cursor', 'text', 'important');
            style.setProperty('pointer-events', 'none', 'important');
            style.setProperty('user-select', 'text', 'important');
            style.setProperty('font-weight', 'normal', 'important');
            style.setProperty('font-size', '14px', 'important');
            style.setProperty('transition', 'none', 'important');
            style.setProperty('transform', 'none', 'important');
            style.setProperty('opacity', '1', 'important');
            style.setProperty('-webkit-appearance', 'none', 'important');
            style.setProperty('-moz-appearance', 'none', 'important');
            style.setProperty('appearance', 'none', 'important');
            
            // Supprimer les classes de bouton
            element.classList.remove('btn', 'btn-primary', 'btn-secondary', 'btn-default', 'btn-outline', 'btn-sm', 'btn-lg', 'btn-block');
            element.classList.remove('button', 'primary', 'secondary');
            
            // Supprimer les événements
            element.onclick = null;
            element.onmousedown = null;
            element.onmouseup = null;
            element.ontouchstart = null;
            element.ontouchend = null;
            
            // Supprimer les attributs
            element.removeAttribute('tabindex');
            element.removeAttribute('role');
            element.removeAttribute('aria-pressed');
            
            console.log('✅ Élément corrigé avec succès');
        });
        
        if (elements.length > 0) {
            console.log('🎯 Fix Noevent: Tous les éléments ont été corrigés !');
        }
    }
    
    // Appliquer immédiatement si le DOM est prêt
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', forceFixNoeventElements);
    } else {
        forceFixNoeventElements();
    }
    
    // Observer pour les nouveaux éléments
    const observer = new MutationObserver(function(mutations) {
        let hasNewNoevent = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && node.classList.contains('noevent')) {
                            hasNewNoevent = true;
                        } else if (node.querySelector && node.querySelector('.noevent')) {
                            hasNewNoevent = true;
                        }
                    }
                });
            }
        });
        
        if (hasNewNoevent) {
            console.log('🔧 Nouveaux éléments .noevent détectés, application de la correction...');
            setTimeout(forceFixNoeventElements, 10);
        }
    });
    
    // Démarrer l'observation
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    // Vérification périodique (au cas où)
    setInterval(function() {
        const elements = document.querySelectorAll('#alleventhours .noevent, .noevent');
        if (elements.length > 0) {
            // Vérifier si un élément a encore l'apparence de bouton
            let needsFix = false;
            elements.forEach(function(el) {
                const computedStyle = window.getComputedStyle(el);
                if (computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' && computedStyle.backgroundColor !== 'transparent') {
                    needsFix = true;
                }
            });
            
            if (needsFix) {
                console.log('🔧 Réapplication de la correction nécessaire...');
                forceFixNoeventElements();
            }
        }
    }, 2000); // Vérifier toutes les 2 secondes
    
    console.log('🚀 Fix Noevent Immediate - Initialisé avec succès !');
})();
