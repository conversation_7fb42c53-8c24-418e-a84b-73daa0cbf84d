﻿@using Core.Themis.Widgets.Offers.Helpers
@model Core.Themis.Widgets.Offers.ViewModels.CategPriceViewModel
@using Microsoft.Extensions.Configuration
@inject IConfiguration Configuration

@*************** VARIABLES *************@
@{
    ViewBag.Title = "Séance";
    string UrlToPath = $"{@Context.Request.Scheme}://{@Context.Request.Host}{@Context.Request.PathBase}";

    if (UrlToPath.Contains("localhost:"))
    {
        UrlToPath = "https://localhost:44310/";
    }
    List<Core.Themis.Libraries.DTO.Translations.TranslationTermDTO> TranslationsList = ViewBag.TranslationsList as List<Core.Themis.Libraries.DTO.Translations.TranslationTermDTO>;
}
@*************** STYLES **************@
@section styles {
    <link rel="stylesheet" href="@Configuration["AssetsUrlPath"]LIBS/newCalendar/1.0.4/css/calendar.css">
    <link rel="stylesheet/less" type="text/css" href="@(UrlToPath)css/Session/style.less">
}
@*<component type="typeof(Counter)" render-mode="ServerPrerendered" />*@

@*************** CONTENT ************@
<div id="oneManifs">

    <div>
        @ViewBag.mySettings
    </div>


    @if (Model.Sessions != null && Model.Sessions.Count > 0)
    {
        <div id="calendarCategsPricesWrapper">
            @if (Model.Sessions.Count > 1)
            {
                <!-- Calendar -->
                <div id="calendarWrapper">
                    <h4 class="text-center">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleSelectOneDate")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleSelectOneDate") : "Sélectionner une date")</h4>
                    <div id="calendarjsWrapper"><div id="calendarjs"></div></div>
                    <hr />
                    <div id="alleventhourstitle" style="display:none;">
                        <h4 class="text-center">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleSelectOneHour")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleSelectOneHour") : "Sélectionner un horaire")</h4>
                    </div>
                    <div id="alleventhours"></div>
                </div>
            }
            <!-- Prices -->

            <div id="categsAndPricesWrapper">

                <h4 class="text-center">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleSelectSeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleSelectSeats") : "Sélectionner des places")</h4>

                <div class="text-center">
                    @if (Model.Sessions.Count > 1)
                    {
                        <a href="#" id="selectedSeance" class="d-inline-block mb-3">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_LblChangeMyDate")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_LblChangeMyDate") : "Je modifie la date :") <span id="selectedSeanceDate"></span></a>
                    }
                    else
                    {
                        <span id="selectedSeance" class="d-inline-block mb-3"><span id="selectedSeanceDate"></span></span>
                    }

                </div>
                @{
                    var classTabs = "nav nav-pills nav-justified d-none flex-column flex-md-row text-center";
                    if (ViewBag.SettingsMerge.sessions.reverseTabsAutoAndSeatPlan == "True")
                    {
                        classTabs = "nav nav-pills nav-justified d-none flex-column-reverse flex-md-row-reverse text-center";
                    }
                }

                <!-- tab choice auto or plan end -->
                <ul class="@classTabs" id="AutoAndSeatPlan" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link btn btn-secondary active" id="auto-tab" href="#ChoiceAuto" data-otherhref="#ChoiceAuto_Add" role="tab" aria-controls="ChoiceAuto" aria-selected="true">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnWeChooseTheBestSeatsForYou")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnWeChooseTheBestSeatsForYou") : "Sélection automatique des meilleures places")</a>
                    </li>
                    <li class="nav-separ">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_LblOr")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_LblOr") : "ou")</li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-secondary" id="plan-tab" href="#ChoicePlan" data-otherhref="#ChoicePlan_Add" role="tab" aria-controls="ChoicePlan" aria-selected="false">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnIChooseMySeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnIChooseMySeats") : "Je choisis mes places sur le plan")</a>
                    </li>
                </ul>
                <div class="d-none" id="TitleFreePlacement">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_LblFreePlacement")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_LblFreePlacement") : "Placement libre")</div>
                <div class="d-none" id="TitleChoiceAuto">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnWeChooseTheBestSeatsForYou")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnWeChooseTheBestSeatsForYou") : "Sélection automatique des meilleures places")</div>
                <div class="d-none" id="TitleChoicePlan">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnIChooseMySeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnIChooseMySeats") : "Je choisis mes places sur le plan")</div>
                <!-- tab choice auto or plan end -->
                <!-- sections start -->
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="ChoiceAuto" role="tabpanel" aria-labelledby="auto-tab">
                        @if (ViewBag.SettingsMerge.sessions.showSeePlan == "True")
                        {
                            <div class="SeePlanSalleGrilleTarif_Wrapper text-center mb-4" style="display:none;">
                                <a class="btn-with-arrow-below collapsed" data-toggle="collapse" href=".SeePlanSalleGrilleTarif" role="button" aria-expanded="false" aria-controls="SeePlanSalleGrilleTarif"><i class="fas fa-eye"></i> @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnSeeSeatingPlan")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnSeeSeatingPlan") : "Voir le plan de salle")</a>
                                <div class="SeePlanSalleGrilleTarif collapse"></div>
                            </div>
                        }
                        <div id="wdgInsertZoneFloorSection"></div>
                        <div id="wdgInsertGrilleTarif">
                            <div class="spinner-border-wrapper text-center"><span class="spinner-border" role="status" aria-hidden="true"></span></div>
                        </div>
                        <!-- if je viens de standalone -->
                        @*<button id="testmodalpanier" onclick="testaffichermodalpanier()">   <span> Ajouter au panier </span>  </button>*@


                        <!-- else ( je viens d'indiv )-->
                        <!-- add to basket AUTO start -->
                        <div id="resumeAddShoppingCartAUTO">
                            <div class="alertMotherRuleMinWrapper"></div>
                            <div class="alertMaxSeatReached"></div>
                            <div id="addPlacesWrapper">
                                <div class="addPlaces btn btn-primary disabled" disabled>
                                    <span>@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnAddToBasket")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnAddToBasket") : "Ajouter à votre panier")</span> - <span class="totalPlacesNumber"></span> : <span class="totalPlacesSpecialAmount"></span> <span class="totalPlacesAmount"></span>
                                </div>
                            </div>
                        </div>
                        <!-- add to basket AUTO end-->
                    </div>
                    <div class="tab-pane fade" id="ChoicePlan" role="tabpanel" aria-labelledby="plan-tab">
                        <div id="wdgInsertSeatPlan">
                            <div class="spinner-border-wrapper text-center"><span class="spinner-border" role="status" aria-hidden="true"></span></div>
                        </div>

                    </div>
                </div>
                <!-- sections end -->
                <!-- total place + bouton ajout panier start-->
                <div class="tab-content" id="myTabContentAddBasket">
                    <!-- add to basket AUTO
                    <div class="tab-pane fade active show" id="ChoiceAuto_Add" role="tabpanel" aria-labelledby="auto-tab">
                        <div id="resumeAddShoppingCart" >
                            <div class="alertMaxSeatReached"></div>
                            <div id="addPlacesWrapper">
                                <div class="addPlaces btn btn-primary disabled" disabled><span>Add to your basket</span> - <span class="totalPlacesNumber"></span><span class="totalPlacesAmount"></span></div>
                            </div>
                        </div>
                     </div>-->
                    <!-- add to basket PLAN
                     <div class="tab-pane fade" id="ChoicePlan_Add" role="tabpanel" aria-labelledby="plan-tab">
                            <div id="resumeAddShoppingCartByPlan" >
                            <div class="alertMaxSeatReached"></div>
                            <div id="addPlacesWrapper">
                                <div class="editPlaces btn btn-secondary">Editer ma sélection</div>
                                <div class="addPlaces btn btn-primary disabled" disabled><span>Add to your basket</span> - <span class="totalPlacesNumber"></span><span class="totalPlacesAmount"></span></div>
                            </div>
                        </div>
                    </div>-->

                </div>
                <!-- total place + bouton ajout panier end-->
            </div>
        </div>
    }

</div>
@*************** MODALS *************@
<template id="alertMotherRuleMinTemplate">
    <div class="alert alert-warning alertMotherRuleMin text-right" role="alert">
        @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgErrorMotherRulesMin")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgErrorMotherRulesMin") : "Plus que <strong>{SeatsNumber} place(s) à ajouter</strong> parmi les tarifs suivants :<br /><strong>{PriceNamesList}</strong>. (trad)")
    </div>
</template>
@*************** MODALS *************@
<!-- modal link avantagecard to prices-->
<div class="modal fade" id="modalLinkAvantageCardToPrices" tabindex="-1" role="dialog" aria-labelledby="modalLinkAvantageCardToPricesTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLinkAvantageCardToPricesTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleAttachAdvantageCard")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleAttachAdvantageCard") : "Attacher une/des carte(s) avantage")</h5>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<!-- modal link feedbook to prices-->
<div class="modal fade" id="modalLinkFeedBookToPrices" tabindex="-1" role="dialog" aria-labelledby="modalLinkFeedBookToPricesTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLinkFeedBookToPricesTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleModalFeedBook")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleModalFeedBook") : "Carnets à tickets (trad)")</h5>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>
<!-- modal ask login to become adherant -->
<div class="modal fade" id="modalLoginBecomeAdherant" tabindex="-1" role="dialog" aria-labelledby="modalLoginBecomeAdherantTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLoginBecomeAdherantTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleModalBecomeMember")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleModalBecomeMember") : "Devenez adhérent")</h5>
            </div>
            <div class="modal-body">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgModalBecomeMember")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgModalBecomeMember") : "Certains tarifs que vous avez sélectionnés font partie d'une ou plusieures offres spéciales adhésion.<br/> Pour bénéficier de ces tarifs particuliers nous vous invitons à vous connecter à votre compte ou bien en créer un.")</div>
            <div class="modal-footer">
                <div class="row text-center text-md-right">
                    <div class="col-12 col-md-auto mb-2 mb-md-0">
                        <button type="button" class="btn btn-secondary" id="modalLoginBecomeAdherantBtnCancel" data-dismiss="modal">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnNotInterestedBecomeMember")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnNotInterestedBecomeMember") : "Je ne suis pas interessé·e")</button>
                    </div>
                    <div class="col-12 col-md-auto mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" id="modalLoginBecomeAdherantBtnCreateAccount">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnSignup")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnSignup") : "Créer un compte")</button>
                    </div>
                    <div class="col-12 col-md-auto mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" id="modalLoginBecomeAdherantBtnLogin">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnLogin")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnLogin") : "Se connecter")</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- modal link consumer to prices-->
<div class="modal fade" id="modalLinkConsumersToPrices" tabindex="-1" role="dialog" aria-labelledby="modalLinkConsumersToPricesTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLinkConsumersToPricesTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleAttachConsumersToPrices")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleAttachConsumersToPrices") : "Identifier les adhérents")</h5>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<!-- modal warning unflag all from plan-->
<div class="modal fade" id="modalUnflagAllFromPlan" tabindex="-1" role="dialog" aria-labelledby="modalUnflagAllFromPlanTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalUnflagAllFromPlanTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_SessionTitleYourSeatsWillBeReleased")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_SessionTitleYourSeatsWillBeReleased") : "Vos places vont être libérées")</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                @Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgYourSeatsWillBeReleased")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgYourSeatsWillBeReleased") : "Vous êtes sur le point de libérer les places sélectionnées,<br />êtes-vous sûr de vouloir quitter le plan ?")
            </div>
            <div class="modal-footer">
                <button type="button" class="mr-auto btn btn-secondary" id="cancelUnflagMySeats">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnCancel")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnCancel") : "Annuler")</button>
                <button type="button" class="btn btn-primary" id="confirmUnflagMySeats">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnFreeMySeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnFreeMySeats") : "Je libère mes places")</button>
            </div>
        </div>
    </div>
</div>

<!-- modal places discontigues -->
<div class="modal fade" id="modalAddBasketPlacesDiscontigues" tabindex="-1" role="dialog" aria-labelledby="modalAddBasketPlacesDiscontiguesTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalAddBasketPlacesDiscontiguesTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalTitleDiscontiguousSeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalTitleDiscontiguousSeats") : "Places non-contigues")</h5>
            </div>
            <div class="modal-body">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalMsgDiscontiguousSeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalMsgDiscontiguousSeats") : "Certaines de ces places ne sont pas contigues, voulez-vous quand même les ajouter à votre panier ?")</div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="modalAddBasketPlacesDiscontiguesBtnCancel" data-dismiss="modal">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnCancel")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnCancel") : "Annuler")</button>
                <button type="button" class="btn btn-primary ml-auto" id="modalAddBasketPlacesDiscontiguesConfirm">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnValidate")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnValidate") : "Valider")</button>
            </div>
        </div>
    </div>
</div>
<!-- modal places strapontins -->
<div class="modal fade" id="modalAddBasketPlacesStrapontins" tabindex="-1" role="dialog" aria-labelledby="modalAddBasketPlacesStrapontinsTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalAddBasketPlacesStrapontinsTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalTitleFoldingSeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalTitleFoldingSeats") : "Places strapontins")</h5>
            </div>
            <div class="modal-body">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalMsgFoldingSeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalMsgFoldingSeats") : "Certaines de ces places sont des strapontins, voulez-vous quand même les ajouter à votre panier ?")</div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="modalAddBasketPlacesStrapontinsBtnCancel" data-dismiss="modal">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnCancel")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnCancel") : "Annuler")</button>
                <button type="button" class="btn btn-primary ml-auto" id="modalAddBasketPlacesStrapontinsConfirm">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnValidate")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnValidate") : "Valider")</button>
            </div>
        </div>
    </div>
</div>
<!-- modal places non contigue ET strapontins -->
<div class="modal fade" id="modalAddBasketPlacesDiscontiguesStrapontins" tabindex="-1" role="dialog" aria-labelledby="modalAddBasketPlacesDiscontiguesStrapontinsTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalAddBasketPlacesDiscontiguesStrapontinsTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalTitleDiscontiguousFoldingSeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalTitleDiscontiguousFoldingSeats") : "Places non-contigues ET strapontins")</h5>
            </div>
            <div class="modal-body">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalMsgDiscontiguousFoldingSeats")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalMsgDiscontiguousFoldingSeats") : "Certaines de ces places ne sont pas contigues et sont des strapontins, voulez-vous quand même les ajouter à votre panier ?")</div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="modalAddBasketPlacesDiscontiguesStrapontinsBtnCancel" data-dismiss="modal">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnCancel")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnCancel") : "Annuler")</button>
                <button type="button" class="btn btn-primary ml-auto" id="modalAddBasketPlacesDiscontiguesStrapontinsConfirm">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnValidate")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnValidate") : "Valider")</button>
            </div>
        </div>
    </div>
</div>
<!-- modal plus de disponibité -->
<div class="modal fade" id="modalAddBasketNotEnoughSeatAvailable" tabindex="-1" role="dialog" aria-labelledby="modalAddBasketNotEnoughSeatAvailableTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalAddBasketNotEnoughSeatAvailableTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalTitleNotEnoughtSeatAvailable")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalTitleNotEnoughtSeatAvailable") : "Plus assez de disponibilité")</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalMsgNotEnoughtSeatAvailable")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_ModalMsgNotEnoughtSeatAvailable") : "Désolé, il semblerait qu'il n'y ait plus assez de places disponibles pour votre demande.<br />Aucune de vos places n'ont été ajoutées au panier.")</div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary ml-auto" id="modalAddBasketNotEnoughSeatAvailableGotIt">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnGotIt")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "BtnGotIt") : "J'ai compris")</button>
            </div>
        </div>
    </div>
</div>

<!-- modal plus de disponibité sur plan, mais dispos en auto -->
<div class="modal fade" id="modalNoDsipoOnPlanButDispoOnAuto" tabindex="-1" role="dialog" aria-labelledby="modalNoDsipoOnPlanButDispoOnAutoTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalNoDsipoOnPlanButDispoOnAutoTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleModalNoDsipoOnPlanButDispoOnAuto")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleModalNoDsipoOnPlanButDispoOnAuto") : "Places disponibles uniquement en automatique")</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgModalNoDsipoOnPlanButDispoOnAuto")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgModalNoDsipoOnPlanButDispoOnAuto") : "Désolé, il semblerait qu'il n'y ait plus assez de places disponibles sur le plan.<br />Cependant, il reste encore des places disponible en placement automatique, souhaitez-vous continuer ?")</div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnNoThanks")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnNoThanks") : "Non, merci")</button>
                <button type="button" class="btn btn-primary ml-auto" id="BtnModalNoDsipoOnPlanButDispoOnAutoContinue">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnContinue")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnContinue") : "Continuer")</button>
            </div>
        </div>
    </div>
</div>

<!-- modal choix après ajout panier -->
<div class="modal fade" id="modalAddBasketChoice" tabindex="-1" role="dialog" aria-labelledby="modalAddBasketChoiceTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl-wide" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalAddBasketChoiceTitle">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleModalAddedToBasket")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_TitleModalAddedToBasket") : "Article ajouté au panier !")</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgModalAddedToBasket")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_MsgModalAddedToBasket") : "Que souhaitez-vous faire maintenant ?")</div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="modalAddBasketChoiceSelectOther">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnSelectOtherEvent")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnSelectOtherEvent") : "Sélectionner une autre manifestation")</button>
                <button type="button" class="btn btn-primary ml-auto" id="modalAddBasketChoiceGoToBasket">@Html.Raw(!string.IsNullOrEmpty(@HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnGoToBasket")) ? @HtmlHelpers.GetTranslationTerm(TranslationsList, "Widget_Session_BtnGoToBasket") : "Aller vers le panier détails")</button>
            </div>
        </div>
    </div>
</div>
<!--modal demander apres choix de place sur plan
<div class="modal"tabindex="-1"id="modal_ask_after_seatchoice"  role="dialog" aria-labelledby="modal_ask_after_seatchoice" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl-wide">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modal_ask_after_seatchoiceTitle">Modal title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Continuer ma sélection</button>
        <button type="button" class="btn btn-primary">J'ai fini de prendre mes places (<span class="totalPlacesNumber"></span>)</button>
      </div>
    </div>
  </div>
</div>-->
@*************** SCRIPTS *************@
@section scripts {
    <script>
        var TranslationsList = @Html.Raw(Json.Serialize(TranslationsList));
        var Basket = @Html.Raw(Json.Serialize(ViewBag.Basket));
        var AllSessions = @Html.Raw(Json.Serialize(Model.Sessions));
        var sessionsData = @Html.Raw(ViewBag.SessionsData);
        var widgetOfferUrl = "@ViewBag.WOfferUrl";
        var widgetCatalogUrl = "@ViewBag.WCatalogUrl";
        var widgetCustomerUrl = "@ViewBag.WCustomerUrl";

        var apiToken = "@ViewBag.Token";
        var widgetSignature = "@ViewBag.WidgetSignature";
        var widgetSignatureCallsGet = "@ViewBag.SignatureWidgetGet";
        var structureId = parseInt("@ViewBag.StructureId");
        var langCode = "@ViewBag.LangCode";
        var htmlSelector = "@ViewBag.HtmlSelector";

        var sWCatalagsUrl = "@ViewBag.WCatalogUrl";
        var eventId = @Html.Raw(ViewBag.EventId);
        var identityId = @Html.Raw(ViewBag.IdentityId);
        var webUserId = @Html.Raw(ViewBag.WebUserId);
        var buyerProfilId = @Html.Raw(ViewBag.BuyerProfilId);
        var partnerToken = "@ViewBag.PartnerToken";
        var deviseCode = @Html.Raw(Json.Serialize(ViewBag.DeviseCode));
        var ForceDate = "@ViewBag.ForceDate";
        var ForceSession = "@ViewBag.ForceSession";
    </script>
    <script src="@(UrlToPath)js/bootstrap-spinner/bootstrap-input-spinner.js"></script>
    <script src="@Configuration["AssetsUrlPath"]LIBS/newCalendar/1.0.4/js/calendar.js"></script>
    <script src="@Configuration["AssetsUrlPath"]pano/@Configuration["PanoFileVersion"]"></script>
    <script src="@Configuration["AssetsUrlPath"]LIBS/interactjs/1.10.11/interact.min.js"></script>

    <script src="@(ViewBag.CustomPackageUrl)"></script>
    <script src="@(UrlToPath)js/Session/session.js"></script>
    <script src="@(UrlToPath)js/session/fix-nodisponible.js"></script>
    <script src="@(UrlToPath)js/session/pano.js"></script>
    <script src="@(UrlToPath)js/session/seatplan.js"></script>
}
