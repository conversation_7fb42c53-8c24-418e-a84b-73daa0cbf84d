<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fix Noevent - Bouton vers Zone de Texte</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .before-after {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
        }
        
        .before {
            background: #ffe6e6;
            border-left: 4px solid #ff4444;
        }
        
        .after {
            background: #e6ffe6;
            border-left: 4px solid #44ff44;
        }
        
        /* Style original problématique (bouton) */
        .noevent-old {
            background: #bbb;
            color: #fff;
            height: 40px;
            line-height: 40px;
            padding: 0 6%;
            display: block;
            border-radius: 1000px;
            text-align: center;
            cursor: pointer;
            margin: 10px 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        
        /* Style corrigé (TEXTE SIMPLE UNIQUEMENT) */
        .noevent,
        .noevent:hover,
        .noevent:focus,
        .noevent:active {
            /* SUPPRESSION COMPLÈTE de toute apparence de bouton */
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            color: #000 !important;
            border: none !important;
            border-radius: 0 !important;
            box-shadow: none !important;
            outline: none !important;

            /* Style de texte simple */
            height: auto !important;
            line-height: 1.5 !important;
            padding: 15px 10px !important;
            margin: 10px 0 !important;
            display: block !important;
            text-align: center !important;
            white-space: normal !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;

            /* Comportement de texte, pas de bouton */
            cursor: text !important;
            pointer-events: none !important;
            user-select: text !important;
            font-weight: normal !important;
            font-size: 14px !important;

            /* Supprimer toute transition ou effet */
            transition: none !important;
            transform: none !important;
            opacity: 1 !important;

            /* S'assurer qu'aucun style de bouton ne s'applique */
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
        }
        
        #alleventhours {
            border: 2px dashed #ccc;
            padding: 20px;
            margin: 20px 0;
            min-height: 100px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Fix Noevent - Transformation Bouton → Zone de Texte</h1>
        <p><strong>Problème :</strong> Le message allemand long était affiché comme un bouton gris tronqué.</p>
        <p><strong>Solution :</strong> SUPPRIMER COMPLÈTEMENT le bouton et afficher uniquement du texte simple noir.</p>
    </div>

    <div class="test-container">
        <h2>📋 Comparaison Avant/Après</h2>
        <div class="before-after">
            <div class="before">
                <h3>❌ AVANT (Problématique)</h3>
                <div class="noevent-old">
                    Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse (069 / 28 45 80) wenden
                </div>
                <small>Texte tronqué, apparence de bouton cliquable</small>
            </div>
            <div class="after">
                <h3>✅ APRÈS (Corrigé)</h3>
                <div class="noevent">
                    Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse (069 / 28 45 80) wenden
                </div>
                <small>Texte complet visible, apparence simple</small>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🧪 Test Dynamique</h2>
        <p>Simuler l'ajout dynamique d'éléments .noevent comme dans le vrai calendrier :</p>
        
        <div id="alleventhours">
            <p><em>Zone de test - Les éléments .noevent apparaîtront ici</em></p>
        </div>
        
        <button class="btn" onclick="addTestMessage()">➕ Ajouter Message Allemand</button>
        <button class="btn" onclick="addTestMessage('fr')">➕ Ajouter Message Français</button>
        <button class="btn" onclick="clearMessages()">🗑️ Vider</button>
        
        <div id="status" class="status"></div>
    </div>

    <div class="test-container">
        <h2>📁 Fichiers Modifiés</h2>
        <ul>
            <li><strong>wwwroot/css/Session/style.less</strong> - CSS corrigé pour .noevent</li>
            <li><strong>wwwroot/js/session/session.js</strong> - Fonction fixNoeventElements() ajoutée</li>
        </ul>
        
        <h3>🎯 Résultat Final</h3>
        <p>Le message allemand complet s'affiche maintenant comme du texte simple gris centré, sans aucune apparence de bouton !</p>
    </div>

    <script>
        // Messages de test
        const messages = {
            de: "Aktuell kein Onlineverkauf. An Vorstellungstagen bitte direkt an die Theaterkasse (069 / 28 45 80) wenden",
            fr: "Pas de séance ce jour - Veuillez contacter directement la billetterie"
        };

        // Fonction pour SUPPRIMER COMPLÈTEMENT toute apparence de bouton (copie de session.js)
        function fixNoeventElements() {
            $('.noevent').each(function() {
                const $element = $(this);

                // SUPPRESSION AGRESSIVE de tous les styles de bouton
                $element.css({
                    'background': 'transparent',
                    'background-color': 'transparent',
                    'background-image': 'none',
                    'color': '#000',
                    'border': 'none',
                    'border-radius': '0',
                    'box-shadow': 'none',
                    'outline': 'none',
                    'height': 'auto',
                    'line-height': '1.5',
                    'padding': '15px 10px',
                    'margin': '10px 0',
                    'display': 'block',
                    'text-align': 'center',
                    'white-space': 'normal',
                    'word-wrap': 'break-word',
                    'overflow-wrap': 'break-word',
                    'cursor': 'text',
                    'pointer-events': 'none',
                    'user-select': 'text',
                    'font-weight': 'normal',
                    'font-size': '14px',
                    'transition': 'none',
                    'transform': 'none',
                    'opacity': '1',
                    '-webkit-appearance': 'none',
                    '-moz-appearance': 'none',
                    'appearance': 'none'
                });

                // Supprimer TOUTES les classes de bouton possibles
                $element.removeClass('btn btn-primary btn-secondary btn-default btn-outline btn-sm btn-lg btn-block');
                $element.removeClass('button primary secondary');

                // Supprimer tous les événements et rendre non-interactif
                $element.off('click mousedown mouseup touchstart touchend');
                $element.removeAttr('tabindex');
                $element.removeAttr('role');
                $element.removeAttr('aria-pressed');
            });
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';

            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 4000);
        }

        function addTestMessage(lang = 'de') {
            const message = messages[lang];
            $('#alleventhours').append(`<span class='noevent'>${message}</span>`);
            
            // Appliquer la correction (comme dans le vrai code)
            setTimeout(fixNoeventElements, 10);
            
            showStatus(`✅ Message ${lang === 'de' ? 'allemand' : 'français'} ajouté - Correction appliquée automatiquement !`, 'success');
        }

        function clearMessages() {
            $('#alleventhours').html('<p><em>Zone de test - Les éléments .noevent apparaîtront ici</em></p>');
            showStatus('🗑️ Messages supprimés', 'info');
        }

        // Observer pour surveiller les changements DOM (comme dans le vrai code)
        const noeventObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if ($(node).hasClass('noevent') || $(node).find('.noevent').length > 0) {
                                setTimeout(fixNoeventElements, 10);
                            }
                        }
                    });
                }
            });
        });

        // Démarrer l'observation
        $(document).ready(function() {
            if (document.getElementById('alleventhours')) {
                noeventObserver.observe(document.getElementById('alleventhours'), {
                    childList: true,
                    subtree: true
                });
            }
            
            showStatus('🚀 Page de test chargée - Prêt pour les tests !', 'success');
        });
    </script>
</body>
</html>
